
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface RoadmapItem {
  id: number;
  title: string;
  description: string;
  status: string;
  targetDate: string;
}

interface KanbanViewProps {
  roadmapItems: RoadmapItem[];
  onRemoveRoadmapItem?: (id: number) => void;
}

const KanbanView: React.FC<KanbanViewProps> = ({ 
  roadmapItems, 
  onRemoveRoadmapItem 
}) => {
  const { toast } = useToast();

  const handleRemoveItem = (id: number) => {
    if (onRemoveRoadmapItem) {
      onRemoveRoadmapItem(id);
    }

    toast({
      title: "Success",
      description: "Roadmap item removed successfully.",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned':
        return 'bg-blue-50 border-blue-200';
      case 'in_progress':
        return 'bg-amber-50 border-amber-200';
      case 'completed':
        return 'bg-green-50 border-green-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getStatusTitle = (status: string) => {
    switch (status) {
      case 'planned':
        return 'Planned';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  };

  const getItemsByStatus = (status: string) => {
    return roadmapItems.filter(item => item.status === status);
  };

  const statuses = ['planned', 'in_progress', 'completed'];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {statuses.map((status) => {
        const items = getItemsByStatus(status);
        return (
          <div key={status} className={`rounded-lg border-2 border-dashed p-4 ${getStatusColor(status)}`}>
            <h3 className="font-semibold text-lg mb-4 text-center">
              {getStatusTitle(status)} ({items.length})
            </h3>
            <div className="space-y-3">
              {items.map((item) => (
                <Card key={item.id} className="transition-all hover:shadow-md">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveItem(item.id)}
                        className="text-destructive hover:text-destructive h-6 w-6 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                      {item.description}
                    </p>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {new Date(item.targetDate).toLocaleDateString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
              {items.length === 0 && (
                <div className="text-center py-8 text-muted-foreground text-sm">
                  No items in {getStatusTitle(status).toLowerCase()}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default KanbanView;
