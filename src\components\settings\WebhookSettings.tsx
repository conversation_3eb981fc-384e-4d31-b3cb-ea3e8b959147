
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Webhook, Send, Eye, Copy, CheckCircle, XCircle } from "lucide-react";

interface WebhookEvent {
  id: string;
  label: string;
  description: string;
  enabled: boolean;
}

const WebhookSettings = () => {
  const { toast } = useToast();
  const [webhookUrl, setWebhookUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [lastTestResult, setLastTestResult] = useState<{ success: boolean; timestamp: Date } | null>(null);
  
  const [webhookEvents, setWebhookEvents] = useState<WebhookEvent[]>([
    { id: "feedback", label: "New Feedback", description: "When new feedback is submitted", enabled: true },
    { id: "bug", label: "Bug Reports", description: "When new bugs are reported", enabled: true },
    { id: "feature", label: "Feature Requests", description: "When new feature requests are submitted", enabled: false },
    { id: "upvote", label: "Upvotes", description: "When items receive new upvotes", enabled: false },
    { id: "status_change", label: "Status Changes", description: "When item status is updated", enabled: false },
  ]);

  const handleEventToggle = (eventId: string) => {
    setWebhookEvents(prev => 
      prev.map(event => 
        event.id === eventId ? { ...event, enabled: !event.enabled } : event
      )
    );
  };

  const handleSaveWebhook = () => {
    if (!webhookUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a webhook URL",
        variant: "destructive",
      });
      return;
    }

    // Here would be the API call to save webhook settings
    console.log("Saving webhook settings:", { webhookUrl, events: webhookEvents });
    
    toast({
      title: "Webhook saved",
      description: "Your webhook configuration has been saved successfully.",
    });
  };

  const handleTestWebhook = async () => {
    if (!webhookUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a webhook URL first",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      const testPayload = {
        event: "test",
        timestamp: new Date().toISOString(),
        data: {
          message: "This is a test webhook from FeedMap",
          project_id: "demo-project-123",
          test: true
        }
      };

      const response = await fetch(webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "FeedMap-Webhook/1.0"
        },
        mode: "no-cors",
        body: JSON.stringify(testPayload),
      });

      setLastTestResult({ success: true, timestamp: new Date() });
      
      toast({
        title: "Test sent",
        description: "Test webhook has been sent. Check your endpoint to verify receipt.",
      });
    } catch (error) {
      setLastTestResult({ success: false, timestamp: new Date() });
      
      toast({
        title: "Test failed",
        description: "Failed to send test webhook. Please check your URL and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Content copied to clipboard",
    });
  };

  const examplePayloads = {
    feedback: {
      event: "feedback_created",
      timestamp: "2025-01-15T10:30:00Z",
      data: {
        id: "fb_123456",
        email: "<EMAIL>",
        feedback: "Great app! Love the new features.",
        rating: 5,
        project_id: "proj_abc123",
        created_at: "2025-01-15T10:30:00Z"
      }
    },
    bug: {
      event: "bug_reported",
      timestamp: "2025-01-15T10:30:00Z",
      data: {
        id: "bug_123456",
        title: "Button not working on mobile",
        description: "The submit button doesn't respond on iOS Safari",
        severity: "medium",
        reporter_email: "<EMAIL>",
        project_id: "proj_abc123",
        created_at: "2025-01-15T10:30:00Z"
      }
    },
    feature: {
      event: "feature_requested",
      timestamp: "2025-01-15T10:30:00Z",
      data: {
        id: "feat_123456",
        title: "Dark mode support",
        description: "Please add dark mode to the application",
        priority: "medium",
        requester_email: "<EMAIL>",
        project_id: "proj_abc123",
        created_at: "2025-01-15T10:30:00Z"
      }
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Webhook className="h-5 w-5" />
            <CardTitle>Webhook Configuration</CardTitle>
          </div>
          <CardDescription>
            Configure webhooks to receive real-time notifications about your project events
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="webhook-url">Webhook URL</Label>
            <div className="flex gap-2">
              <Input
                id="webhook-url"
                placeholder="https://your-app.com/webhooks/feedmap"
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                className="flex-1"
              />
              <Button 
                onClick={handleTestWebhook}
                disabled={isLoading || !webhookUrl.trim()}
                variant="outline"
                className="shrink-0"
              >
                <Send className="h-4 w-4 mr-2" />
                {isLoading ? "Testing..." : "Test"}
              </Button>
            </div>
            {lastTestResult && (
              <div className="flex items-center gap-2 text-sm">
                {lastTestResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className={lastTestResult.success ? "text-green-600" : "text-red-600"}>
                  Last test: {lastTestResult.success ? "Successful" : "Failed"} at{" "}
                  {lastTestResult.timestamp.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <Label>Event Notifications</Label>
            <div className="space-y-3">
              {webhookEvents.map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{event.label}</span>
                      {event.enabled && <Badge variant="secondary" className="text-xs">Active</Badge>}
                    </div>
                    <p className="text-sm text-muted-foreground">{event.description}</p>
                  </div>
                  <Switch
                    checked={event.enabled}
                    onCheckedChange={() => handleEventToggle(event.id)}
                  />
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSaveWebhook}>
              Save Webhook Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            <CardTitle>Webhook Documentation</CardTitle>
          </div>
          <CardDescription>
            Learn about the webhook payload structure and how to handle incoming requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="feedback">Feedback</TabsTrigger>
              <TabsTrigger value="bugs">Bug Reports</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">How it works</h4>
                  <p className="text-sm text-muted-foreground">
                    When enabled events occur in your project, FeedMap will send HTTP POST requests to your webhook URL 
                    with a JSON payload containing event details.
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Headers</h4>
                  <div className="bg-muted p-3 rounded-md text-sm font-mono">
                    Content-Type: application/json<br/>
                    User-Agent: FeedMap-Webhook/1.0<br/>
                    X-FeedMap-Event: {"{event_type}"}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Response</h4>
                  <p className="text-sm text-muted-foreground">
                    Your endpoint should respond with a 2xx status code to acknowledge receipt. 
                    Failed deliveries will be retried up to 3 times with exponential backoff.
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="feedback" className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">Feedback Event Payload</h4>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => copyToClipboard(JSON.stringify(examplePayloads.feedback, null, 2))}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto">
                    <code>{JSON.stringify(examplePayloads.feedback, null, 2)}</code>
                  </pre>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="bugs" className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">Bug Report Event Payload</h4>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => copyToClipboard(JSON.stringify(examplePayloads.bug, null, 2))}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto">
                    <code>{JSON.stringify(examplePayloads.bug, null, 2)}</code>
                  </pre>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="features" className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">Feature Request Event Payload</h4>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => copyToClipboard(JSON.stringify(examplePayloads.feature, null, 2))}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto">
                    <code>{JSON.stringify(examplePayloads.feature, null, 2)}</code>
                  </pre>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default WebhookSettings;
