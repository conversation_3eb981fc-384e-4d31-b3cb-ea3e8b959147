
import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

export interface WebhookEvent {
  id: string;
  label: string;
  description: string;
  enabled: boolean;
}

export interface WebhookConfig {
  url: string;
  events: WebhookEvent[];
  isActive: boolean;
}

export const useWebhooks = () => {
  const { toast } = useToast();
  const [config, setConfig] = useState<WebhookConfig>({
    url: '',
    events: [
      { id: "feedback", label: "New Feedback", description: "When new feedback is submitted", enabled: true },
      { id: "bug", label: "Bug Reports", description: "When new bugs are reported", enabled: true },
      { id: "feature", label: "Feature Requests", description: "When new feature requests are submitted", enabled: false },
      { id: "upvote", label: "Upvotes", description: "When items receive new upvotes", enabled: false },
      { id: "status_change", label: "Status Changes", description: "When item status is updated", enabled: false },
    ],
    isActive: false
  });

  const updateUrl = useCallback((url: string) => {
    setConfig(prev => ({ ...prev, url }));
  }, []);

  const toggleEvent = useCallback((eventId: string) => {
    setConfig(prev => ({
      ...prev,
      events: prev.events.map(event => 
        event.id === eventId ? { ...event, enabled: !event.enabled } : event
      )
    }));
  }, []);

  const saveConfig = useCallback(async () => {
    try {
      // Here would be the API call to save webhook configuration
      console.log('Saving webhook config:', config);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setConfig(prev => ({ ...prev, isActive: true }));
      
      toast({
        title: "Webhook saved",
        description: "Your webhook configuration has been saved successfully.",
      });
      
      return true;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save webhook configuration. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  }, [config, toast]);

  const testWebhook = useCallback(async () => {
    try {
      const testPayload = {
        event: "test",
        timestamp: new Date().toISOString(),
        data: {
          message: "This is a test webhook from FeedMap",
          project_id: "demo-project-123",
          test: true
        }
      };

      const response = await fetch(config.url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "FeedMap-Webhook/1.0",
          "X-FeedMap-Event": "test"
        },
        mode: "no-cors",
        body: JSON.stringify(testPayload),
      });

      toast({
        title: "Test sent",
        description: "Test webhook has been sent. Check your endpoint to verify receipt.",
      });
      
      return true;
    } catch (error) {
      toast({
        title: "Test failed",
        description: "Failed to send test webhook. Please check your URL and try again.",
        variant: "destructive",
      });
      return false;
    }
  }, [config.url, toast]);

  const sendWebhook = useCallback(async (eventType: string, data: any) => {
    if (!config.isActive || !config.url) return;

    const eventConfig = config.events.find(e => e.id === eventType);
    if (!eventConfig?.enabled) return;

    try {
      const payload = {
        event: eventType,
        timestamp: new Date().toISOString(),
        data
      };

      await fetch(config.url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "FeedMap-Webhook/1.0",
          "X-FeedMap-Event": eventType
        },
        mode: "no-cors",
        body: JSON.stringify(payload),
      });

      console.log(`Webhook sent for event: ${eventType}`, payload);
    } catch (error) {
      console.error(`Failed to send webhook for event: ${eventType}`, error);
    }
  }, [config]);

  return {
    config,
    updateUrl,
    toggleEvent,
    saveConfig,
    testWebhook,
    sendWebhook
  };
};
