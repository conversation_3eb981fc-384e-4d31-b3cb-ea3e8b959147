
import React from "react";
import AppLayout from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// Mock data for charts
const weeklyData = [
  { name: '<PERSON>', feedback: 4, bugs: 2, features: 3 },
  { name: '<PERSON><PERSON>', feedback: 3, bugs: 1, features: 2 },
  { name: 'Wed', feedback: 5, bugs: 3, features: 1 },
  { name: 'Thu', feedback: 7, bugs: 0, features: 4 },
  { name: 'Fri', feedback: 3, bugs: 1, features: 2 },
  { name: '<PERSON><PERSON>', feedback: 2, bugs: 0, features: 1 },
  { name: '<PERSON>', feedback: 1, bugs: 0, features: 0 },
];

const monthlyData = [
  { name: 'Jan', feedback: 24, bugs: 12, features: 18 },
  { name: 'Feb', feedback: 32, bugs: 15, features: 21 },
  { name: 'Mar', feedback: 28, bugs: 10, features: 19 },
  { name: 'Apr', feedback: 35, bugs: 18, features: 23 },
  { name: 'May', feedback: 40, bugs: 20, features: 25 },
  { name: 'Jun', feedback: 38, bugs: 17, features: 22 },
];

const sentimentData = [
  { name: 'Positive', value: 70 },
  { name: 'Neutral', value: 20 },
  { name: 'Negative', value: 10 },
];

const Analytics = () => {
  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold">Analytics</h1>
            <p className="text-muted-foreground mt-1">Analyze feedback trends and user interactions</p>
          </div>
          
          <div className="flex gap-4">
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select project" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Projects</SelectItem>
                <SelectItem value="dev_imob_host">Dev Imob Host</SelectItem>
                <SelectItem value="crm_system">CRM System</SelectItem>
              </SelectContent>
            </Select>
            
            <Select defaultValue="30d">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Time period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="year">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-xl">Total Feedback</CardTitle>
              <CardDescription>All types of user input</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">187</div>
              <p className="text-muted-foreground text-sm flex items-center mt-1">
                <span className="text-green-500 mr-1">↑ 12%</span> vs previous period
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-xl">Response Time</CardTitle>
              <CardDescription>Average time to respond</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">4.2h</div>
              <p className="text-muted-foreground text-sm flex items-center mt-1">
                <span className="text-red-500 mr-1">↑ 0.8h</span> vs previous period
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-xl">Resolution Rate</CardTitle>
              <CardDescription>Issues marked as resolved</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">76%</div>
              <p className="text-muted-foreground text-sm flex items-center mt-1">
                <span className="text-green-500 mr-1">↑ 5%</span> vs previous period
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
            <TabsTrigger value="bugs">Bug Reports</TabsTrigger>
            <TabsTrigger value="features">Feature Requests</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Feedback Overview</CardTitle>
                <CardDescription>All types of feedback over time</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="feedback" stroke="#8884d8" name="Feedback" />
                    <Line type="monotone" dataKey="bugs" stroke="#ff0000" name="Bug Reports" />
                    <Line type="monotone" dataKey="features" stroke="#00C49F" name="Feature Requests" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="feedback" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Feedback Analysis</CardTitle>
                <CardDescription>Detailed feedback metrics</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={weeklyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="feedback" fill="#8884d8" name="Feedback" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="bugs" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Bug Report Analysis</CardTitle>
                <CardDescription>Bug report metrics</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={weeklyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="bugs" fill="#ff0000" name="Bug Reports" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="features" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Feature Request Analysis</CardTitle>
                <CardDescription>Feature request metrics</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={weeklyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="features" fill="#00C49F" name="Feature Requests" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
};

export default Analytics;
