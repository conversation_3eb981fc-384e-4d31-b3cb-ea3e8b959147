
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface ContactInfoCardProps {
  contact: {
    email: string;
    phone: string;
    address: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({ contact, onChange }) => {
  return (
    <div className="space-y-4">
      <p className="text-sm text-muted-foreground mb-4">
        Provide contact details that will be displayed in your widget
      </p>
      
      <div className="space-y-2">
        <Label htmlFor="contact-email">Email Address</Label>
        <Input
          id="contact-email"
          name="email"
          type="email"
          placeholder="<EMAIL>"
          value={contact.email}
          onChange={onChange}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="contact-phone">Phone Number</Label>
        <Input
          id="contact-phone"
          name="phone"
          type="tel"
          placeholder="+****************"
          value={contact.phone}
          onChange={onChange}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="contact-address">Business Address</Label>
        <Textarea
          id="contact-address"
          name="address"
          placeholder="123 Business Street, City, State 12345, Country"
          value={contact.address}
          onChange={onChange}
          rows={3}
        />
      </div>
    </div>
  );
};

export default ContactInfoCard;
