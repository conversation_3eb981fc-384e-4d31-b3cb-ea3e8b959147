
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Check, X } from "lucide-react";

export const getStatusBadge = (status: string) => {
  switch(status) {
    case 'unread':
      return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">Unread</Badge>;
    case 'read':
      return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-300">Read</Badge>;
    case 'completed':
      return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300 flex items-center gap-1"><Check className="h-3 w-3" /> Completed</Badge>;
    case 'pending':
      return <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-300">Pending</Badge>;
    case 'fixed':
      return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300 flex items-center gap-1"><Check className="h-3 w-3" /> Fixed</Badge>;
    case 'planned':
      return <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">Planned</Badge>;
    case 'in_progress':
      return <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">In Progress</Badge>;
    default:
      return null;
  }
};
