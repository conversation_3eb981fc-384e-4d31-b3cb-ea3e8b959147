
export interface WebhookPayload {
  event: string;
  timestamp: string;
  data: any;
}

export class WebhookService {
  private static instance: WebhookService;
  private webhookUrl: string = '';
  private enabledEvents: string[] = [];

  static getInstance(): WebhookService {
    if (!WebhookService.instance) {
      WebhookService.instance = new WebhookService();
    }
    return WebhookService.instance;
  }

  configure(url: string, enabledEvents: string[]) {
    this.webhookUrl = url;
    this.enabledEvents = enabledEvents;
  }

  async sendWebhook(eventType: string, data: any): Promise<boolean> {
    if (!this.webhookUrl || !this.enabledEvents.includes(eventType)) {
      return false;
    }

    try {
      const payload: WebhookPayload = {
        event: eventType,
        timestamp: new Date().toISOString(),
        data
      };

      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'FeedMap-Webhook/1.0',
          'X-FeedMap-Event': eventType
        },
        mode: 'no-cors',
        body: JSON.stringify(payload)
      });

      console.log(`Webhook sent successfully for event: ${eventType}`);
      return true;
    } catch (error) {
      console.error(`Failed to send webhook for event: ${eventType}`, error);
      return false;
    }
  }

  // Métodos específicos para diferentes tipos de eventos
  async onFeedbackCreated(feedback: any) {
    return this.sendWebhook('feedback_created', {
      id: feedback.id,
      email: feedback.email,
      feedback: feedback.feedback,
      rating: feedback.rating,
      project_id: feedback.project_id,
      created_at: feedback.created_at
    });
  }

  async onBugReported(bug: any) {
    return this.sendWebhook('bug_reported', {
      id: bug.id,
      title: bug.title,
      description: bug.description,
      severity: bug.severity,
      reporter_email: bug.reporter_email,
      project_id: bug.project_id,
      created_at: bug.created_at
    });
  }

  async onFeatureRequested(feature: any) {
    return this.sendWebhook('feature_requested', {
      id: feature.id,
      title: feature.title,
      description: feature.description,
      priority: feature.priority,
      requester_email: feature.requester_email,
      project_id: feature.project_id,
      created_at: feature.created_at
    });
  }

  async onUpvoteAdded(upvote: any) {
    return this.sendWebhook('upvote_added', {
      id: upvote.id,
      item_id: upvote.item_id,
      item_type: upvote.item_type,
      user_email: upvote.user_email,
      total_upvotes: upvote.total_upvotes,
      project_id: upvote.project_id,
      created_at: upvote.created_at
    });
  }

  async onStatusChanged(item: any) {
    return this.sendWebhook('status_changed', {
      id: item.id,
      item_type: item.item_type,
      old_status: item.old_status,
      new_status: item.new_status,
      changed_by: item.changed_by,
      project_id: item.project_id,
      changed_at: item.changed_at
    });
  }
}

export const webhookService = WebhookService.getInstance();
