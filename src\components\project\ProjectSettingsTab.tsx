
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useToast } from "@/components/ui/use-toast";
import { Trash2 } from "lucide-react";

type ProjectSettingsProps = {
  project: any;
};

const ProjectSettingsTab = ({ project }: ProjectSettingsProps) => {
  const [projectData, setProjectData] = useState({
    name: project.name,
    domain: project.domain,
    greeting: project.greeting,
    enabled: { ...project.enabled },
    contact: { ...project.contact },
    faq: [...(project.faq || [])]
  });

  const { toast } = useToast();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProjectData(prev => ({ ...prev, [name]: value }));
  };

  const handleToggle = (feature: string) => {
    setProjectData(prev => ({
      ...prev,
      enabled: {
        ...prev.enabled,
        [feature]: !prev.enabled[feature as keyof typeof prev.enabled]
      }
    }));
  };

  const handleContactChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProjectData(prev => ({
      ...prev,
      contact: {
        ...prev.contact,
        [name]: value
      }
    }));
  };

  const handleFaqChange = (id: number, field: string, value: string) => {
    setProjectData(prev => ({
      ...prev,
      faq: prev.faq.map((item: any) => 
        item.id === id ? { ...item, [field]: value } : item
      )
    }));
  };

  const addFaq = () => {
    const newId = projectData.faq.length > 0 
      ? Math.max(...projectData.faq.map((item: any) => item.id)) + 1 
      : 0;
    
    setProjectData(prev => ({
      ...prev,
      faq: [...prev.faq, { id: newId, question: "", answer: "" }]
    }));
  };

  const removeFaq = (id: number) => {
    setProjectData(prev => ({
      ...prev,
      faq: prev.faq.filter((item: any) => item.id !== id)
    }));
  };

  const handleSave = () => {
    // Here would go API call to update project settings
    
    toast({
      title: "Settings saved",
      description: "Your project settings have been updated successfully."
    });
  };

  const handleDelete = () => {
    // Here would go API call to delete project
    
    toast({
      title: "Project deleted",
      description: "Your project has been deleted permanently."
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Basic Settings</CardTitle>
          <CardDescription>Update your project information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name</Label>
            <Input
              id="name"
              name="name"
              value={projectData.name}
              onChange={handleChange}
              placeholder="My Awesome Project"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="domain">Domain</Label>
            <Input
              id="domain"
              name="domain"
              value={projectData.domain}
              onChange={handleChange}
              placeholder="example.com"
            />
            <p className="text-sm text-muted-foreground">
              Your widget will only work on this domain
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="greeting">Greeting Text</Label>
            <Input
              id="greeting"
              name="greeting"
              value={projectData.greeting}
              onChange={handleChange}
              placeholder="Hey 👋"
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Features</CardTitle>
          <CardDescription>Enable or disable widget features</CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion type="multiple" defaultValue={["feedback", "bug", "feature", "faq", "contact"]}>
            <AccordionItem value="feedback">
              <AccordionTrigger className="py-3">
                <div className="flex justify-between w-full pr-4">
                  <span>Feedback Collection</span>
                  <Switch 
                    checked={projectData.enabled.feedback}
                    onCheckedChange={() => handleToggle('feedback')}
                  />
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <p className="text-muted-foreground mb-4">
                  Allow users to provide general feedback about your product or service.
                </p>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="bug">
              <AccordionTrigger className="py-3">
                <div className="flex justify-between w-full pr-4">
                  <span>Bug Reports</span>
                  <Switch 
                    checked={projectData.enabled.bug}
                    onCheckedChange={() => handleToggle('bug')}
                  />
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <p className="text-muted-foreground mb-4">
                  Allow users to report bugs and issues they encounter.
                </p>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="feature">
              <AccordionTrigger className="py-3">
                <div className="flex justify-between w-full pr-4">
                  <span>Feature Requests</span>
                  <Switch 
                    checked={projectData.enabled.feature}
                    onCheckedChange={() => handleToggle('feature')}
                  />
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <p className="text-muted-foreground mb-4">
                  Allow users to suggest new features or improvements.
                </p>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="faq">
              <AccordionTrigger className="py-3">
                <div className="flex justify-between w-full pr-4">
                  <span>FAQ</span>
                  <Switch 
                    checked={projectData.enabled.faq}
                    onCheckedChange={() => handleToggle('faq')}
                  />
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground mb-4">
                    Display frequently asked questions to help users find answers quickly.
                  </p>
                  
                  {projectData.enabled.faq && (
                    <>
                      <div className="space-y-4">
                        {projectData.faq.map((item: any) => (
                          <div key={item.id} className="border rounded-md p-4 space-y-3">
                            <div className="flex justify-between items-center">
                              <h4 className="text-sm font-medium">FAQ #{item.id + 1}</h4>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFaq(item.id)}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </div>
                            
                            <div className="space-y-2">
                              <Label htmlFor={`question-${item.id}`}>Question</Label>
                              <Input
                                id={`question-${item.id}`}
                                value={item.question}
                                onChange={(e) => handleFaqChange(item.id, 'question', e.target.value)}
                              />
                            </div>
                            
                            <div className="space-y-2">
                              <Label htmlFor={`answer-${item.id}`}>Answer</Label>
                              <Textarea
                                id={`answer-${item.id}`}
                                rows={3}
                                value={item.answer}
                                onChange={(e) => handleFaqChange(item.id, 'answer', e.target.value)}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      <Button variant="outline" onClick={addFaq}>
                        Add FAQ Item
                      </Button>
                    </>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="contact">
              <AccordionTrigger className="py-3">
                <div className="flex justify-between w-full pr-4">
                  <span>Contact Information</span>
                  <Switch 
                    checked={projectData.enabled.contact}
                    onCheckedChange={() => handleToggle('contact')}
                  />
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground mb-4">
                    Provide contact details for users who need direct assistance.
                  </p>
                  
                  {projectData.enabled.contact && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="contact-email">Email</Label>
                        <Input
                          id="contact-email"
                          name="email"
                          value={projectData.contact.email}
                          onChange={handleContactChange}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="contact-phone">Phone</Label>
                        <Input
                          id="contact-phone"
                          name="phone"
                          value={projectData.contact.phone}
                          onChange={handleContactChange}
                          placeholder="+****************"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="contact-address">Address</Label>
                        <Textarea
                          id="contact-address"
                          name="address"
                          value={projectData.contact.address}
                          onChange={handleContactChange}
                          placeholder="123 Main St, City, Country"
                          rows={3}
                        />
                      </div>
                    </>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Installation</CardTitle>
          <CardDescription>Add the FeedMap widget to your website</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm">
              Copy and paste this code snippet just before the closing <code className="bg-muted px-1 py-0.5 rounded">&lt;/body&gt;</code> tag of your website:
            </p>
            
            <div className="bg-muted p-4 rounded-md">
              <pre className="text-xs overflow-x-auto">
                <code>{`<script>
  (function(w,d,s,o,f,js,fjs){
    w['FeedMap']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};
    js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
    js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
  }(window,document,'script','feedmap','https://cdn.feedmap.io/widget.js'));
  feedmap('init', '${project.id}');
</script>`}</code>
              </pre>
            </div>
            
            <Button variant="outline" onClick={() => {
              navigator.clipboard.writeText(`<script>
  (function(w,d,s,o,f,js,fjs){
    w['FeedMap']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};
    js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
    js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
  }(window,document,'script','feedmap','https://cdn.feedmap.io/widget.js'));
  feedmap('init', '${project.id}');
</script>`);
              
              toast({
                title: "Code copied",
                description: "Installation code copied to clipboard!"
              });
            }}>
              Copy to Clipboard
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <div className="flex justify-between">
        <Button variant="destructive" onClick={handleDelete}>
          Delete Project
        </Button>
        
        <Button onClick={handleSave}>
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default ProjectSettingsTab;
