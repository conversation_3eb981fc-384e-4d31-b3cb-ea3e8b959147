
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { TooltipProvider } from "@/components/ui/tooltip";
import AppLayout from "@/components/layout/AppLayout";
import ProjectHeader from "@/components/project/ProjectHeader";
import ProjectTabs from "@/components/project/ProjectTabs";
import ProjectContent from "@/components/project/ProjectContent";
import { projectData, changelogData } from "@/data/mockProjectData";
import { useProjectState } from "@/hooks/useProjectState";
import { useStatusHandlers } from "@/hooks/useStatusHandlers";
import { useFaqHandlers } from "@/hooks/useFaqHandlers";
import { useRoadmapChangelogHandlers } from "@/hooks/useRoadmapChangelogHandlers";

const ProjectDetails = () => {
  const { projectId } = useParams();
  const [activeTab, setActiveTab] = useState("feedback");

  const {
    project,
    setProject,
    changelog,
    setChangelog,
    formData,
    setFormData,
    handleInputChange,
    handleContactChange,
    handleToggleChange,
    handleSaveSettings
  } = useProjectState(projectData, changelogData);

  const {
    updateFeedbackStatus,
    updateBugStatus,
    updateFeatureStatus,
    addToRoadmap,
    addToUpvote,
    handleVote
  } = useStatusHandlers(project, setProject);

  const {
    handleFaqChange,
    handleAddFaq,
    handleRemoveFaq
  } = useFaqHandlers(formData, setFormData);

  const {
    handleAddRoadmapItem,
    handleRemoveRoadmapItem,
    handleAddChangelogEntry,
    handleRemoveChangelogEntry
  } = useRoadmapChangelogHandlers(project, setProject, changelog, setChangelog);

  const handleSettingsClick = () => {
    setActiveTab("settings");
  };

  return (
    <AppLayout>
      <TooltipProvider>
        <div className="container mx-auto py-8 px-4">
          <ProjectHeader 
            name={project.name} 
            domain={project.domain} 
            onSettingsClick={handleSettingsClick}
          />
          
          <ProjectTabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab}>
            <ProjectContent
              project={project}
              changelog={changelog}
              formData={formData}
              onUpdateFeedbackStatus={updateFeedbackStatus}
              onUpdateBugStatus={updateBugStatus}
              onUpdateFeatureStatus={updateFeatureStatus}
              onAddToRoadmap={addToRoadmap}
              onAddToUpvote={addToUpvote}
              onVote={handleVote}
              onAddRoadmapItem={handleAddRoadmapItem}
              onRemoveRoadmapItem={handleRemoveRoadmapItem}
              onAddChangelogEntry={handleAddChangelogEntry}
              onRemoveChangelogEntry={handleRemoveChangelogEntry}
              onInputChange={handleInputChange}
              onContactChange={handleContactChange}
              onToggleChange={handleToggleChange}
              onSaveSettings={handleSaveSettings}
              onFaqChange={handleFaqChange}
              onAddFaq={handleAddFaq}
              onRemoveFaq={handleRemoveFaq}
            />
          </ProjectTabs>
        </div>
      </TooltipProvider>
    </AppLayout>
  );
};

export default ProjectDetails;
