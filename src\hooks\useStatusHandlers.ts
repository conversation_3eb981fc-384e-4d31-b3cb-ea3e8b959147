
import { useToast } from "@/hooks/use-toast";
import { Project, FeatureRequest, UpvoteItem } from "@/types/project";

export const useStatusHandlers = (
  project: Project, 
  setProject: React.Dispatch<React.SetStateAction<Project>>
) => {
  const { toast } = useToast();

  const updateFeedbackStatus = (index: number, newStatus: string) => {
    const updatedFeedback = [...project.feedback];
    updatedFeedback[index] = {
      ...updatedFeedback[index],
      status: newStatus
    };

    toast({
      title: "Status Updated",
      description: `Feedback status updated to "${newStatus}".`,
    });

    setProject(prev => ({
      ...prev,
      feedback: updatedFeedback
    }));
  };

  const updateBugStatus = (index: number, newStatus: string) => {
    const updatedBugs = [...project.bug];
    updatedBugs[index] = {
      ...updatedBugs[index],
      status: newStatus
    };

    toast({
      title: "Status Updated",
      description: `Bug report status updated to "${newStatus}".`,
    });

    setProject(prev => ({
      ...prev,
      bug: updatedBugs
    }));
  };

  const updateFeatureStatus = (index: number, newStatus: string) => {
    const updatedFeatures = [...project.feature];
    updatedFeatures[index] = {
      ...updatedFeatures[index],
      status: newStatus
    };

    toast({
      title: "Status Updated",
      description: `Feature request status updated to "${newStatus}".`,
    });

    setProject(prev => ({
      ...prev,
      feature: updatedFeatures
    }));
  };

  const addToRoadmap = (feature: FeatureRequest) => {
    const newRoadmapItem = {
      id: project.roadmap.length + 1,
      title: feature.feature,
      description: feature.detail,
      status: "planned",
      targetDate: new Date().toISOString().split('T')[0]
    };

    toast({
      title: "Added to Roadmap",
      description: `"${feature.feature}" has been added to the roadmap.`,
    });

    setProject(prev => ({
      ...prev,
      roadmap: [...prev.roadmap, newRoadmapItem]
    }));
  };

  const addToUpvote = (feature: FeatureRequest) => {
    const newUpvoteItem: UpvoteItem = {
      id: project.upvotes.length + 1,
      feature: feature.feature,
      detail: feature.detail,
      upvotes: 0,
      date: Date.now()
    };

    toast({
      title: "Added to Upvotes",
      description: `"${feature.feature}" has been added to upvotes for community voting.`,
    });

    setProject(prev => ({
      ...prev,
      upvotes: [...prev.upvotes, newUpvoteItem]
    }));
  };

  const handleVote = (id: number) => {
    const updatedUpvotes = project.upvotes.map(item => 
      item.id === id ? { ...item, upvotes: item.upvotes + 1 } : item
    );

    toast({
      title: "Vote Recorded",
      description: "Your vote has been recorded. Thank you!",
    });

    setProject(prev => ({
      ...prev,
      upvotes: updatedUpvotes
    }));
  };

  return {
    updateFeedbackStatus,
    updateBugStatus,
    updateFeatureStatus,
    addToRoadmap,
    addToUpvote,
    handleVote
  };
};
