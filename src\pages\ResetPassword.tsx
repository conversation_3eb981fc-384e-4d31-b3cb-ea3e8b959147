import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, ArrowLeft } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

const ResetPassword = () => {
  const [emailSent, setEmailSent] = useState(false);
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // In a real app, you'd handle password reset request here
    setEmailSent(true);
    toast({
      title: "Reset email sent",
      description:
        "If an account exists with this email, you'll receive instructions to reset your password.",
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50 px-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-flex items-center text-primary text-2xl font-medium">
            <img
              src="/Logo.png"
              alt="FeedMap Logo"
              width={30}
              height={30}
              className="mr-1"
            />
            <span>
              Feed<span className="font-bold">Map</span>
            </span>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Map your Feedback, Feed your Roadmap
          </p>
        </div>

        <Card className="border-none shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">Reset password</CardTitle>
            <CardDescription>
              {!emailSent
                ? "Enter your email to receive a password reset link"
                : "Check your email for reset instructions"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!emailSent ? (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <Button type="submit" className="w-full">
                  Send reset link
                </Button>
              </form>
            ) : (
              <div className="text-center py-4">
                <div className="bg-primary/10 text-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  If an account with that email exists, we've sent instructions
                  to reset your password.
                </p>
                <Button
                  variant="ghost"
                  className="mt-4"
                  onClick={() => setEmailSent(false)}
                >
                  Try a different email
                </Button>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <div className="text-center w-full">
              <Link
                to="/login"
                className="text-sm text-primary font-medium hover:underline inline-flex items-center"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to login
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ResetPassword;
