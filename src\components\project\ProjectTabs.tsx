
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Bug,
  MessageSquare,
  PlusCircle,
  BarChart2,
  ScrollText,
  ThumbsUp,
} from "lucide-react";

interface ProjectTabsProps {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
}

const ProjectTabs: React.FC<ProjectTabsProps> = ({
  defaultValue = "feedback",
  value,
  onValueChange,
  children,
}) => {
  return (
    <Tabs defaultValue={defaultValue} value={value} onValueChange={onValueChange}>
      <div className="overflow-x-auto pb-2 mb-6">
        <TabsList className="grid w-full grid-cols-6 min-w-max lg:min-w-0 h-auto p-1">
          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger
                value="feedback"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 px-2 py-2 text-xs sm:text-sm min-w-0"
              >
                <MessageSquare className="h-5 w-5 flex-shrink-0" />
                <span className="hidden xs:inline truncate">Feedback</span>
              </TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>View user feedback and comments</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger
                value="bugs"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 px-2 py-2 text-xs sm:text-sm min-w-0"
              >
                <Bug className="h-5 w-5 flex-shrink-0" />
                <span className="hidden xs:inline truncate">Bug Reports</span>
              </TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Manage bug reports from users</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger
                value="features"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 px-2 py-2 text-xs sm:text-sm min-w-0"
              >
                <PlusCircle className="h-5 w-5 flex-shrink-0" />
                <span className="hidden xs:inline truncate">Feature Requests</span>
              </TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>View and manage feature requests</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger
                value="upvotes"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 px-2 py-2 text-xs sm:text-sm min-w-0"
              >
                <ThumbsUp className="h-5 w-5 flex-shrink-0" />
                <span className="hidden xs:inline truncate">Upvotes</span>
              </TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Track community upvotes on features</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger
                value="roadmap"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 px-2 py-2 text-xs sm:text-sm min-w-0"
              >
                <BarChart2 className="h-5 w-5 flex-shrink-0" />
                <span className="hidden xs:inline truncate">Roadmap</span>
              </TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>View project roadmap and milestones</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger
                value="changelog"
                className="flex flex-col sm:flex-row items-center justify-center gap-1 px-2 py-2 text-xs sm:text-sm min-w-0"
              >
                <ScrollText className="h-5 w-5 flex-shrink-0" />
                <span className="hidden xs:inline truncate">Changelog</span>
              </TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>View project changelog and updates</p>
            </TooltipContent>
          </Tooltip>
        </TabsList>
      </div>
      {children}
    </Tabs>
  );
};

export default ProjectTabs;
