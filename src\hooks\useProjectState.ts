
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Project } from "@/types/project";

export const useProjectState = (initialProject: Project, initialChangelog: any[]) => {
  const { toast } = useToast();
  const [project, setProject] = useState<Project>(initialProject);
  const [changelog, setChangelog] = useState(initialChangelog);
  const [formData, setFormData] = useState({
    name: project.name,
    domain: project.domain,
    greeting: project.greeting,
    contact: project.contact,
    enabled: project.enabled,
    faq: project.faq
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContactChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      contact: {
        ...prev.contact,
        [name]: value
      }
    }));
  };

  const handleToggleChange = (feature: keyof typeof formData.enabled) => {
    setFormData(prev => ({
      ...prev,
      enabled: {
        ...prev.enabled,
        [feature]: !prev.enabled[feature]
      }
    }));
  };

  const handleSaveSettings = () => {
    setProject({
      ...project,
      ...formData
    });

    toast({
      title: "Settings Updated",
      description: "Your project settings have been saved successfully.",
    });
  };

  return {
    project,
    setProject,
    changelog,
    setChangelog,
    formData,
    setFormData,
    handleInputChange,
    handleContactChange,
    handleToggleChange,
    handleSaveSettings
  };
};
