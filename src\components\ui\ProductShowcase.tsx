
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import MeowButton from "./MeowButton";

interface Product {
  id: string;
  name: string;
  description: string;
  image: string;
  features: string[];
}

interface ProductShowcaseProps {
  products: Product[];
  className?: string;
}

const ProductShowcase: React.FC<ProductShowcaseProps> = ({
  products,
  className,
}) => {
  const [activeProduct, setActiveProduct] = useState(0);

  const handleNext = () => {
    setActiveProduct((prev) => (prev + 1) % products.length);
  };

  const handlePrev = () => {
    setActiveProduct((prev) => (prev - 1 + products.length) % products.length);
  };

  return (
    <div className={cn("relative overflow-hidden py-10", className)}>
      <div className="max-w-7xl mx-auto px-6 md:px-10">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
          <div>
            <AnimatePresence mode="wait">
              <motion.div
                key={activeProduct}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                <motion.div className="inline-block">
                  <span className="text-xs font-medium bg-secondary rounded-full px-3 py-1">
                    Featured Product
                  </span>
                </motion.div>
                <h2 className="text-3xl md:text-4xl font-medium tracking-tight">
                  {products[activeProduct].name}
                </h2>
                <p className="text-muted-foreground">
                  {products[activeProduct].description}
                </p>
                <ul className="space-y-2">
                  {products[activeProduct].features.map((feature, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex items-center"
                    >
                      <span className="h-2 w-2 rounded-full bg-primary mr-2" />
                      <span className="text-sm">{feature}</span>
                    </motion.li>
                  ))}
                </ul>
                <div className="pt-2">
                  <MeowButton>Learn more</MeowButton>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          <div className="relative h-[400px] md:h-[500px]">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeProduct}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <div className="relative w-full h-full">
                  <img
                    src={products[activeProduct].image}
                    alt={products[activeProduct].name}
                    className="w-full h-full object-cover object-center rounded-xl"
                  />
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/10 rounded-xl" />
                </div>
              </motion.div>
            </AnimatePresence>

            <div className="absolute bottom-4 right-4 flex space-x-2">
              <button
                onClick={handlePrev}
                className="p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors duration-200"
                aria-label="Previous product"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m15 18-6-6 6-6" />
                </svg>
              </button>
              <button
                onClick={handleNext}
                className="p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition-colors duration-200"
                aria-label="Next product"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductShowcase;
