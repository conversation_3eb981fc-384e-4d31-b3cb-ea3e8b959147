
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Menu } from "lucide-react";
import DesktopSidebar from "./DesktopSidebar";
import MobileHeader from "./MobileHeader";

const AppLayout = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);

  // Check if we're on mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
      // If transitioning to mobile and sidebar is collapsed, expand it
      if (window.innerWidth < 768 && collapsed) {
        setCollapsed(false);
      }
    };

    // Check on initial render
    checkMobileView();
    
    // Add resize listener
    window.addEventListener('resize', checkMobileView);
    
    // Clean up
    return () => window.removeEventListener('resize', checkMobileView);
  }, [collapsed]);

  const handleLogout = () => {
    // In a real app, you'd handle logout logic here
    navigate("/login");
  };

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar for desktop */}
      <DesktopSidebar 
        collapsed={collapsed} 
        toggleSidebar={toggleSidebar} 
        handleLogout={handleLogout} 
      />

      {/* Main content with mobile sidebar trigger */}
      <div className="flex-1 overflow-auto bg-background text-foreground">
        <MobileHeader handleLogout={handleLogout} />
        <div className="p-4 sm:p-6 md:p-8">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AppLayout;
