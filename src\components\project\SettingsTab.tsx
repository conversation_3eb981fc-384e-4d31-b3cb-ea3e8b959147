
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import FaqItemsCard from "./FaqItemsCard";

interface SettingsTabProps {
  formData: {
    name: string;
    domain: string;
    greeting: string;
    contact: {
      address: string;
      phone: string;
      email: string;
    };
    enabled: {
      feedback: boolean;
      bug: boolean;
      feature: boolean;
      faq: boolean;
      contact: boolean;
    };
    faq?: Array<{
      id: number;
      question: string;
      answer: string;
    }>;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleContactChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleToggleChange: (feature: string) => void;
  handleSaveSettings: () => void;
  onFaqChange?: (id: number, field: "question" | "answer", value: string) => void;
  onAddFaq?: () => void;
  onRemoveFaq?: (id: number) => void;
}

const SettingsTab: React.FC<SettingsTabProps> = ({
  formData,
  handleInputChange,
  handleContactChange,
  handleToggleChange,
  handleSaveSettings,
  onFaqChange,
  onAddFaq,
  onRemoveFaq
}) => {
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Project Settings</h2>
          <p className="text-muted-foreground">
            Configure your project settings and widget features
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>Update your project details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="My Awesome Project"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="domain">Domain</Label>
            <Input
              id="domain"
              name="domain"
              value={formData.domain}
              onChange={handleInputChange}
              placeholder="example.com"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="greeting">Greeting Message</Label>
            <Input
              id="greeting"
              name="greeting"
              value={formData.greeting}
              onChange={handleInputChange}
              placeholder="Hey 👋"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Widget Features</CardTitle>
          <CardDescription>Enable or disable features in your feedback widget</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Feedback Collection</Label>
              <p className="text-sm text-muted-foreground">Allow users to submit general feedback</p>
            </div>
            <Switch
              checked={formData.enabled.feedback}
              onCheckedChange={() => handleToggleChange('feedback')}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Bug Reports</Label>
              <p className="text-sm text-muted-foreground">Allow users to report bugs and issues</p>
            </div>
            <Switch
              checked={formData.enabled.bug}
              onCheckedChange={() => handleToggleChange('bug')}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Feature Requests</Label>
              <p className="text-sm text-muted-foreground">Allow users to suggest new features</p>
            </div>
            <Switch
              checked={formData.enabled.feature}
              onCheckedChange={() => handleToggleChange('feature')}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>FAQ Section</Label>
              <p className="text-sm text-muted-foreground">Display frequently asked questions</p>
            </div>
            <Switch
              checked={formData.enabled.faq}
              onCheckedChange={() => handleToggleChange('faq')}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Contact Information</Label>
              <p className="text-sm text-muted-foreground">Show contact details to users</p>
            </div>
            <Switch
              checked={formData.enabled.contact}
              onCheckedChange={() => handleToggleChange('contact')}
            />
          </div>
        </CardContent>
      </Card>

      {formData.enabled.faq && (
        <Card>
          <CardHeader>
            <CardTitle>FAQ Items</CardTitle>
            <CardDescription>Manage your frequently asked questions</CardDescription>
          </CardHeader>
          <CardContent>
            <FaqItemsCard
              faq={formData.faq || []}
              onFaqChange={onFaqChange || (() => {})}
              onAddFaq={onAddFaq || (() => {})}
              onRemoveFaq={onRemoveFaq || (() => {})}
            />
          </CardContent>
        </Card>
      )}

      {formData.enabled.contact && (
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Set up your contact details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="contact-email">Email</Label>
              <Input
                id="contact-email"
                name="email"
                value={formData.contact.email}
                onChange={handleContactChange}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contact-phone">Phone</Label>
              <Input
                id="contact-phone"
                name="phone"
                value={formData.contact.phone}
                onChange={handleContactChange}
                placeholder="+****************"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contact-address">Address</Label>
              <Textarea
                id="contact-address"
                name="address"
                value={formData.contact.address}
                onChange={handleContactChange}
                placeholder="123 Main Street, City, Country"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-end">
        <Button onClick={handleSaveSettings}>
          Save Settings
        </Button>
      </div>
    </div>
  );
};

export default SettingsTab;
