import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>itle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Bug, MessageSquare, PlusCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import AppLayout from "@/components/layout/AppLayout";

// Mock data - would be replaced with real data from API
const projects = [
  {
    id: "6dR0EJL642KqjFddl9sI",
    name: "Dev Imob Host",
    domain: "dev.imob.host",
    feedback: Array(8).fill({}),
    bug: Array(3).fill({}),
    feature: Array(5).fill({}),
  },
  {
    id: "7eS1FKM753LrkHfeem0J",
    name: "CRM System",
    domain: "crm.example.com",
    feedback: <PERSON>rray(12).fill({}),
    bug: Array(7).fill({}),
    feature: Array(9).fill({}),
  },
];

const Dashboard = () => {
  const [selectedProject, setSelectedProject] = useState(projects[0]);

  const handleProjectChange = (projectId: string) => {
    const project = projects.find((p) => p.id === projectId);
    if (project) {
      setSelectedProject(project);
    }
  };

  return (
    <AppLayout>
      <div className="w-full max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 md:mb-8 gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground mt-1">
              Monitor your projects' feedback and reports
            </p>
          </div>

          <div className="flex flex-col sm:flex-row w-full sm:w-auto gap-3">
            <Select
              value={selectedProject.id}
              onValueChange={handleProjectChange}
            >
              <SelectTrigger className="w-full sm:w-[200px] md:w-[240px]">
                <SelectValue placeholder="Select project" />
              </SelectTrigger>
              <SelectContent>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button asChild className="w-full sm:w-auto">
              <Link to="/projects/new">
                <PlusCircle className="mr-2 h-4 w-4" />
                New Project
              </Link>
            </Button>
          </div>
        </div>
        <div className="mt-6 md:mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="break-words">
                {selectedProject.name}
              </CardTitle>
              <CardDescription className="break-all">
                {selectedProject.domain}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Total Items
                  </p>
                  <p className="text-2xl font-bold">
                    {selectedProject.feedback.length +
                      selectedProject.bug.length +
                      selectedProject.feature.length}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Most Recent
                  </p>
                  <p className="text-md font-medium">5 minutes ago</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Status
                  </p>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <p className="text-md font-medium">Active</p>
                  </div>
                </div>
                <div className="flex items-center justify-start lg:justify-end mt-2 lg:mt-0">
                  <Button asChild className="w-full sm:w-auto">
                    <Link to={`/projects/${selectedProject.id}`}>
                      View Project Details
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        <h2 className="text-xl md:text-2xl font-bold mb-4 mt-8">
          Project Overview
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {/* Feedback Card */}
          <Card className="shadow-sm hover:shadow-md transition-shadow h-full flex flex-col">
            <CardHeader className="pb-2 flex-shrink-0">
              <div className="flex justify-between items-center">
                <CardTitle className="text-2xl">Feedback</CardTitle>
                <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-2xl">
                  <MessageSquare className="text-blue-600 dark:text-blue-400 h-6 w-6" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col justify-center">
              <div className="text-4xl font-bold">
                {selectedProject.feedback.length}
              </div>
              <p className="text-muted-foreground text-sm">
                User opinions and suggestions
              </p>
            </CardContent>
            <CardFooter className="flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                asChild
                className="w-full sm:w-auto"
              >
                <Link to={`/projects/${selectedProject.id}/feedback`}>
                  View all feedback
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Bug Reports Card */}
          <Card className="shadow-sm hover:shadow-md transition-shadow h-full flex flex-col">
            <CardHeader className="pb-2 flex-shrink-0">
              <div className="flex justify-between items-center">
                <CardTitle className="text-2xl">Bug Reports</CardTitle>
                <div className="bg-red-100 dark:bg-red-900 p-3 rounded-2xl">
                  <Bug className="text-red-600 dark:text-red-400 h-6 w-6" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col justify-center">
              <div className="text-4xl font-bold">
                {selectedProject.bug.length}
              </div>
              <p className="text-muted-foreground text-sm">
                Reported issues and errors
              </p>
            </CardContent>
            <CardFooter className="flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                asChild
                className="w-full sm:w-auto"
              >
                <Link to={`/projects/${selectedProject.id}/bugs`}>
                  View all bugs
                </Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Feature Requests Card */}
          <Card className="shadow-sm hover:shadow-md transition-shadow h-full flex flex-col">
            <CardHeader className="pb-2 flex-shrink-0">
              <div className="flex justify-between items-center">
                <CardTitle className="text-2xl">Feature Requests</CardTitle>
                <div className="bg-green-100 dark:bg-green-900 p-3 rounded-2xl">
                  <PlusCircle className="text-green-600 dark:text-green-400 h-6 w-6" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col justify-center">
              <div className="text-4xl font-bold">
                {selectedProject.feature.length}
              </div>
              <p className="text-muted-foreground text-sm">
                Requested new features
              </p>
            </CardContent>
            <CardFooter className="flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                asChild
                className="w-full sm:w-auto"
              >
                <Link to={`/projects/${selectedProject.id}/features`}>
                  View all requests
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
};

export default Dashboard;
