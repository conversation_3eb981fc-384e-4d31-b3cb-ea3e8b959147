import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { formatDistanceToNow } from "date-fns";

interface ChangelogEntry {
  id: number;
  version: string;
  title: string;
  description: string;
  date: number;
  type: "feature" | "bugfix" | "improvement" | "breaking";
  changes: string[];
}

interface ChangelogTabProps {
  changelog: ChangelogEntry[];
  onAddChangelogEntry?: (entry: Omit<ChangelogEntry, "id" | "date">) => void;
  onRemoveChangelogEntry?: (id: number) => void;
}

const ChangelogTab: React.FC<ChangelogTabProps> = ({
  changelog,
  onAddChangelogEntry,
  onRemoveChangelogEntry,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newEntry, setNewEntry] = useState({
    version: "",
    title: "",
    description: "",
    type: "feature" as const,
    changes: [""],
  });
  const { toast } = useToast();

  const handleAddEntry = () => {
    if (!newEntry.version || !newEntry.title || !newEntry.description) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    const filteredChanges = newEntry.changes.filter(
      (change) => change.trim() !== ""
    );
    if (filteredChanges.length === 0) {
      toast({
        title: "Error",
        description: "Please add at least one change item.",
        variant: "destructive",
      });
      return;
    }

    if (onAddChangelogEntry) {
      onAddChangelogEntry({
        ...newEntry,
        changes: filteredChanges,
      });
    }

    setNewEntry({
      version: "",
      title: "",
      description: "",
      type: "feature",
      changes: [""],
    });
    setIsDialogOpen(false);

    toast({
      title: "Success",
      description: "Changelog entry added successfully.",
    });
  };

  const handleRemoveEntry = (id: number) => {
    if (onRemoveChangelogEntry) {
      onRemoveChangelogEntry(id);
    }

    toast({
      title: "Success",
      description: "Changelog entry removed successfully.",
    });
  };

  const addChangeItem = () => {
    setNewEntry((prev) => ({
      ...prev,
      changes: [...prev.changes, ""],
    }));
  };

  const updateChangeItem = (index: number, value: string) => {
    setNewEntry((prev) => ({
      ...prev,
      changes: prev.changes.map((change, i) => (i === index ? value : change)),
    }));
  };

  const removeChangeItem = (index: number) => {
    setNewEntry((prev) => ({
      ...prev,
      changes: prev.changes.filter((_, i) => i !== index),
    }));
  };

  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case "feature":
        return "default";
      case "bugfix":
        return "destructive";
      case "improvement":
        return "secondary";
      case "breaking":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "feature":
        return "New Feature";
      case "bugfix":
        return "Bug Fix";
      case "improvement":
        return "Improvement";
      case "breaking":
        return "Breaking Change";
      default:
        return "Update";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Changelog</h2>
          <p className="text-muted-foreground">
            Keep track of all project updates and changes
          </p>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Changelog Entry
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Changelog Entry</DialogTitle>
              <DialogDescription>
                Document a new release or update to your project.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="version">Version *</Label>
                  <Input
                    id="version"
                    value={newEntry.version}
                    onChange={(e) =>
                      setNewEntry((prev) => ({
                        ...prev,
                        version: e.target.value,
                      }))
                    }
                    placeholder="1.0.0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select
                    value={newEntry.type}
                    onValueChange={(value: any) =>
                      setNewEntry((prev) => ({ ...prev, type: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="feature">New Feature</SelectItem>
                      <SelectItem value="bugfix">Bug Fix</SelectItem>
                      <SelectItem value="improvement">Improvement</SelectItem>
                      <SelectItem value="breaking">Breaking Change</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={newEntry.title}
                  onChange={(e) =>
                    setNewEntry((prev) => ({ ...prev, title: e.target.value }))
                  }
                  placeholder="Enhanced User Experience"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={newEntry.description}
                  onChange={(e) =>
                    setNewEntry((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Brief description of the changes in this release..."
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label>Changes *</Label>
                <div className="space-y-2">
                  {newEntry.changes.map((change, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={change}
                        onChange={(e) =>
                          updateChangeItem(index, e.target.value)
                        }
                        placeholder="Added new feature..."
                        className="flex-1"
                      />
                      {newEntry.changes.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeChangeItem(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addChangeItem}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Change Item
                  </Button>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddEntry}>Add Entry</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {changelog.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">
                No changelog entries yet
              </h3>
              <p className="text-muted-foreground mb-4">
                Start documenting your project updates and changes.
              </p>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Entry
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Add New Changelog Entry</DialogTitle>
                    <DialogDescription>
                      Document a new release or update to your project.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="version">Version *</Label>
                        <Input
                          id="version"
                          value={newEntry.version}
                          onChange={(e) =>
                            setNewEntry((prev) => ({
                              ...prev,
                              version: e.target.value,
                            }))
                          }
                          placeholder="1.0.0"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="type">Type</Label>
                        <Select
                          value={newEntry.type}
                          onValueChange={(value: any) =>
                            setNewEntry((prev) => ({ ...prev, type: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="feature">New Feature</SelectItem>
                            <SelectItem value="bugfix">Bug Fix</SelectItem>
                            <SelectItem value="improvement">
                              Improvement
                            </SelectItem>
                            <SelectItem value="breaking">
                              Breaking Change
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="title">Title *</Label>
                      <Input
                        id="title"
                        value={newEntry.title}
                        onChange={(e) =>
                          setNewEntry((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                        placeholder="Enhanced User Experience"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description *</Label>
                      <Textarea
                        id="description"
                        value={newEntry.description}
                        onChange={(e) =>
                          setNewEntry((prev) => ({
                            ...prev,
                            description: e.target.value,
                          }))
                        }
                        placeholder="Brief description of the changes in this release..."
                        rows={3}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Changes *</Label>
                      <div className="space-y-2">
                        {newEntry.changes.map((change, index) => (
                          <div key={index} className="flex gap-2">
                            <Input
                              value={change}
                              onChange={(e) =>
                                updateChangeItem(index, e.target.value)
                              }
                              placeholder="Added new feature..."
                              className="flex-1"
                            />
                            {newEntry.changes.length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeChangeItem(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addChangeItem}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Change Item
                        </Button>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleAddEntry}>Add Entry</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {changelog.map((entry) => (
            <Card key={entry.id} className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                  <div className="flex items-center gap-3">
                    <CardTitle className="text-lg">
                      v{entry.version} - {entry.title}
                    </CardTitle>
                    <Badge variant={getTypeBadgeVariant(entry.type)}>
                      {getTypeLabel(entry.type)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(entry.date), {
                        addSuffix: true,
                      })}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveEntry(entry.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <CardDescription className="text-base">
                  {entry.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Changes:
                  </h4>
                  <ul className="space-y-1">
                    {entry.changes.map((change, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <span className="text-muted-foreground mt-1">•</span>
                        <span>{change}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChangelogTab;
