
import { Project } from "@/types/project";

export const projectData: Project = {
  id: "6dR0EJL642KqjFddl9sI",
  name: "Dev Imob Host",
  domain: "dev.imob.host",
  greeting: "Hey 👋",
  feedback: [
    {
      email: "<EMAIL>",
      feedback: "I love the new dashboard design. It's much more intuitive than the previous version.",
      date: 1747369945018,
      status: "read"
    },
    {
      email: "<EMAIL>",
      feedback: "Could you add dark mode support? My eyes hurt when using the app at night.",
      date: 1747269945018,
      status: "unread"
    },
    {
      email: "<EMAIL>",
      feedback: "The loading times have improved significantly after the latest update.",
      date: 1747169945018,
      status: "completed"
    }
  ],
  bug: [
    {
      bug: "Login page freezes",
      email: "<EMAIL>",
      steps: "1. Go to login page\n2. Enter username and password\n3. Click login button\n4. Page freezes and nothing happens",
      date: 1747369967818,
      status: "pending"
    },
    {
      bug: "Images not loading",
      email: "<EMAIL>",
      steps: "Images don't load on the property listing page. I see broken image icons instead.",
      date: 1747269967818,
      status: "fixed"
    }
  ],
  feature: [
    {
      date: 1747369894581,
      feature: "Advanced filtering",
      detail: "Please add more advanced filtering options to the property search feature. I want to filter by specific amenities.",
      status: "planned"
    },
    {
      feature: "Email notifications",
      detail: "Add ability to receive email notifications when new properties match my saved search criteria.",
      date: 1747369986915,
      status: "in_progress"
    },
    {
      feature: "Mobile app",
      detail: "Would be great to have a mobile app for checking listings on the go.",
      date: 1747269986915,
      status: "completed"
    }
  ],
  contact: {
    address: "123 Main Street, Suite 101, San Francisco, CA 94103",
    phone: "+****************",
    email: "<EMAIL>"
  },
  enabled: {
    feedback: true,
    bug: true,
    feature: true,
    faq: true,
    contact: true
  },
  faq: [
    {
      question: "How do I reset my password?",
      answer: "To reset your password, click on the 'Forgot Password' link on the login page and follow the instructions sent to your email.",
      id: 0
    },
    {
      answer: "Our standard support hours are Monday to Friday, 9am to 5pm Pacific Time.",
      id: 1,
      question: "What are your support hours?"
    }
  ],
  roadmap: [
    {
      id: 1,
      title: "Mobile Application",
      description: "Develop native mobile apps for iOS and Android",
      status: "planned",
      targetDate: "2025-09-01"
    },
    {
      id: 2,
      title: "Advanced Analytics",
      description: "Add more detailed analytics and reporting features",
      status: "in_progress",
      targetDate: "2025-07-15"
    },
    {
      id: 3,
      title: "API Improvements",
      description: "Enhance the API with more endpoints and better documentation",
      status: "completed",
      targetDate: "2025-05-30"
    }
  ],
  upvotes: [
    {
      id: 1,
      feature: "Dark Mode",
      detail: "Add a dark mode theme option for better visibility at night",
      upvotes: 24,
      date: 1747369945018
    },
    {
      id: 2,
      feature: "Export Data",
      detail: "Add ability to export listing data to CSV or Excel format",
      upvotes: 18,
      date: 1747269945018
    }
  ]
};

export const changelogData = [
  {
    id: 1,
    version: "2.1.0",
    title: "Enhanced User Experience",
    description: "Major improvements to the user interface and new features for better property management.",
    date: 1747369945018,
    type: "feature" as const,
    changes: [
      "Added advanced filtering options for property search",
      "Improved dashboard layout and responsiveness",
      "New property comparison feature",
      "Enhanced map integration with better markers"
    ]
  },
  {
    id: 2,
    version: "2.0.5",
    title: "Bug Fixes and Performance",
    description: "Critical bug fixes and performance optimizations.",
    date: 1747269945018,
    type: "bugfix" as const,
    changes: [
      "Fixed login page freezing issue",
      "Resolved image loading problems on property listings",
      "Improved page load speeds by 40%",
      "Fixed mobile navigation menu bugs"
    ]
  },
  {
    id: 3,
    version: "2.0.0",
    title: "Major Platform Update",
    description: "Complete redesign with new architecture and features.",
    date: 1747169945018,
    type: "breaking" as const,
    changes: [
      "New modern user interface design",
      "Rebuilt property search engine",
      "Added user authentication system",
      "Migration to new database structure"
    ]
  }
];
