
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollText } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface ChangelogEntry {
  id: number;
  version: string;
  title: string;
  description: string;
  date: number;
  type: "feature" | "bugfix" | "improvement" | "breaking";
  changes: string[];
}

interface PublicChangelogTabProps {
  changelog: ChangelogEntry[];
}

const PublicChangelogTab: React.FC<PublicChangelogTabProps> = ({ changelog }) => {
  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case "feature":
        return "default";
      case "bugfix":
        return "destructive";
      case "improvement":
        return "secondary";
      case "breaking":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "feature":
        return "New Feature";
      case "bugfix":
        return "Bug Fix";
      case "improvement":
        return "Improvement";
      case "breaking":
        return "Breaking Change";
      default:
        return "Update";
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Changelog</h2>
        <p className="text-muted-foreground">
          Stay updated with the latest project changes and releases
        </p>
      </div>

      {changelog.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <ScrollText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-muted-foreground">No changelog entries available yet.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {changelog
            .sort((a, b) => b.date - a.date)
            .map((entry) => (
              <Card key={entry.id} className="transition-all hover:shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                    <div className="flex items-center gap-3">
                      <CardTitle className="text-lg">
                        v{entry.version} - {entry.title}
                      </CardTitle>
                      <Badge variant={getTypeBadgeVariant(entry.type)}>
                        {getTypeLabel(entry.type)}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(entry.date), { addSuffix: true })}
                    </div>
                  </div>
                  <CardDescription className="text-base">
                    {entry.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Changes:</h4>
                    <ul className="space-y-1">
                      {entry.changes.map((change, index) => (
                        <li key={index} className="flex items-start gap-2 text-sm">
                          <span className="text-muted-foreground mt-1">•</span>
                          <span>{change}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      )}
    </div>
  );
};

export default PublicChangelogTab;
