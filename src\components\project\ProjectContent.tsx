
import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import FeedbackTab from "@/components/project/FeedbackTab";
import BugReportsTab from "@/components/project/BugReportsTab";
import FeatureRequestsTab from "@/components/project/FeatureRequestsTab";
import UpvotesTab from "@/components/project/UpvotesTab";
import RoadmapTab from "@/components/project/RoadmapTab";
import SettingsTab from "@/components/project/SettingsTab";
import ChangelogTab from "@/components/project/ChangelogTab";
import { Project } from "@/types/project";

interface ProjectContentProps {
  project: Project;
  changelog: any[];
  formData: any;
  onUpdateFeedbackStatus: (index: number, newStatus: string) => void;
  onUpdateBugStatus: (index: number, newStatus: string) => void;
  onUpdateFeatureStatus: (index: number, newStatus: string) => void;
  onAddToRoadmap: (feature: any) => void;
  onAddToUpvote: (feature: any) => void;
  onVote: (id: number) => void;
  onAddRoadmapItem: (item: any) => void;
  onRemoveRoadmapItem: (id: number) => void;
  onAddChangelogEntry: (entry: any) => void;
  onRemoveChangelogEntry: (id: number) => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onContactChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onToggleChange: (feature: any) => void;
  onSaveSettings: () => void;
  onFaqChange: (id: number, field: "question" | "answer", value: string) => void;
  onAddFaq: () => void;
  onRemoveFaq: (id: number) => void;
}

const ProjectContent: React.FC<ProjectContentProps> = ({
  project,
  changelog,
  formData,
  onUpdateFeedbackStatus,
  onUpdateBugStatus,
  onUpdateFeatureStatus,
  onAddToRoadmap,
  onAddToUpvote,
  onVote,
  onAddRoadmapItem,
  onRemoveRoadmapItem,
  onAddChangelogEntry,
  onRemoveChangelogEntry,
  onInputChange,
  onContactChange,
  onToggleChange,
  onSaveSettings,
  onFaqChange,
  onAddFaq,
  onRemoveFaq
}) => {
  return (
    <>
      <TabsContent value="feedback">
        <FeedbackTab 
          feedback={project.feedback} 
          onUpdateStatus={onUpdateFeedbackStatus}
        />
      </TabsContent>
      
      <TabsContent value="bugs">
        <BugReportsTab 
          bugReports={project.bug} 
          onUpdateStatus={onUpdateBugStatus}
        />
      </TabsContent>
      
      <TabsContent value="features">
        <FeatureRequestsTab 
          featureRequests={project.feature}
          onUpdateStatus={onUpdateFeatureStatus}
          onAddToRoadmap={onAddToRoadmap}
          onAddToUpvote={onAddToUpvote}
        />
      </TabsContent>
      
      <TabsContent value="upvotes">
        <UpvotesTab 
          upvotes={project.upvotes}
          onVote={onVote}
        />
      </TabsContent>
      
      <TabsContent value="roadmap">
        <RoadmapTab 
          roadmapItems={project.roadmap}
          onAddRoadmapItem={onAddRoadmapItem}
          onRemoveRoadmapItem={onRemoveRoadmapItem}
        />
      </TabsContent>

      <TabsContent value="changelog">
        <ChangelogTab 
          changelog={changelog}
          onAddChangelogEntry={onAddChangelogEntry}
          onRemoveChangelogEntry={onRemoveChangelogEntry}
        />
      </TabsContent>
      
      <TabsContent value="settings">
        <SettingsTab 
          formData={formData}
          handleInputChange={onInputChange}
          handleContactChange={onContactChange}
          handleToggleChange={onToggleChange}
          handleSaveSettings={onSaveSettings}
          onFaqChange={onFaqChange}
          onAddFaq={onAddFaq}
          onRemoveFaq={onRemoveFaq}
        />
      </TabsContent>
    </>
  );
};

export default ProjectContent;
