
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { 
  FileText, Download, ChevronDown, ChevronUp, Plus, Trash2, 
  Copy, Eye, EyeOff, Save, RotateCw, AlignLeft, Layers, Columns,
  Plus as PlusIcon, Sun, Moon, Grid3x3, PanelLeft, Calendar, Clock, Info,
  LayoutGrid, LayoutList
} from "lucide-react";
import MeowButton from "@/components/ui/MeowButton";
import { useToast } from "@/components/ui/use-toast";
import { useTheme } from "../ThemeProvider";
import { format } from "date-fns";

const TemplateDemo = () => {
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const [formLayout, setFormLayout] = useState("vertical");
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({
    "basic-info": true,
    "project-details": true,
    "payment-terms": false,
    "team-members": true,
    "milestones": true
  });
  
  // State for header expansion
  const [headerExpanded, setHeaderExpanded] = useState(false);
  
  // Document metadata
  const createdDate = new Date(2023, 6, 15, 9, 30);
  const updatedDate = new Date();
  
  // State for loop groups (e.g., multiple project milestones)
  const [milestones, setMilestones] = useState([
    { id: "milestone-1", title: "Project Kickoff", date: "2023-08-01", description: "Initial project meeting and requirements gathering", amount: "5000" },
    { id: "milestone-2", title: "Design Phase", date: "2023-09-15", description: "Complete all design deliverables", amount: "7500" }
  ]);
  
  // State for active milestone tab
  const [activeMilestoneTab, setActiveMilestoneTab] = useState("milestone-1");
  
  // State for team members loop group
  const [teamMembers, setTeamMembers] = useState([
    { id: "member-1", name: "John Doe", role: "Project Manager", email: "<EMAIL>", rate: "150" },
    { id: "member-2", name: "Jane Smith", role: "Lead Developer", email: "<EMAIL>", rate: "125" }
  ]);
  
  const [activeTeamMemberTab, setActiveTeamMemberTab] = useState("member-1");
  
  // State for preview mode
  const [previewMode, setPreviewMode] = useState(false);
  
  // State for canvas layout sections
  const [activeSections, setActiveSections] = useState<string[]>(["basic-info", "project-details"]);
  const [focusedSection, setFocusedSection] = useState<string | null>(null);
  
  const toggleGroup = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };
  
  const addMilestone = () => {
    const newId = `milestone-${milestones.length + 1}`;
    const newMilestone = { 
      id: newId, 
      title: `Milestone ${milestones.length + 1}`, 
      date: "", 
      description: "", 
      amount: "0" 
    };
    setMilestones([...milestones, newMilestone]);
    setActiveMilestoneTab(newId);
    
    toast({
      title: "Milestone Added",
      description: "New milestone has been added to the project.",
    });
  };
  
  const removeMilestone = (id: string) => {
    if (milestones.length <= 1) {
      toast({
        title: "Cannot Remove",
        description: "At least one milestone is required.",
        variant: "destructive"
      });
      return;
    }
    
    const newMilestones = milestones.filter(m => m.id !== id);
    setMilestones(newMilestones);
    
    // Set active tab to first milestone if we're removing the active one
    if (activeMilestoneTab === id) {
      setActiveMilestoneTab(newMilestones[0].id);
    }
    
    toast({
      title: "Milestone Removed",
      description: "The milestone has been removed from the project.",
    });
  };
  
  const duplicateMilestone = (id: string) => {
    const milestoneToClone = milestones.find(m => m.id === id);
    if (!milestoneToClone) return;
    
    const newId = `milestone-${milestones.length + 1}`;
    const clonedMilestone = { 
      ...milestoneToClone, 
      id: newId,
      title: `${milestoneToClone.title} (Copy)`
    };
    
    setMilestones([...milestones, clonedMilestone]);
    setActiveMilestoneTab(newId);
    
    toast({
      title: "Milestone Duplicated",
      description: "The milestone has been duplicated.",
    });
  };
  
  const addTeamMember = () => {
    const newId = `member-${teamMembers.length + 1}`;
    const newMember = { 
      id: newId, 
      name: "", 
      role: "", 
      email: "", 
      rate: "0" 
    };
    setTeamMembers([...teamMembers, newMember]);
    setActiveTeamMemberTab(newId);
    
    toast({
      title: "Team Member Added",
      description: "New team member has been added to the project.",
    });
  };
  
  const removeTeamMember = (id: string) => {
    if (teamMembers.length <= 1) {
      toast({
        title: "Cannot Remove",
        description: "At least one team member is required.",
        variant: "destructive"
      });
      return;
    }
    
    const newTeamMembers = teamMembers.filter(m => m.id !== id);
    setTeamMembers(newTeamMembers);
    
    // Set active tab to first member if we're removing the active one
    if (activeTeamMemberTab === id) {
      setActiveTeamMemberTab(newTeamMembers[0].id);
    }
    
    toast({
      title: "Team Member Removed",
      description: "The team member has been removed from the project.",
    });
  };
  
  const updateMilestone = (id: string, field: string, value: string) => {
    setMilestones(prev => 
      prev.map(m => 
        m.id === id ? { ...m, [field]: value } : m
      )
    );
  };
  
  const updateTeamMember = (id: string, field: string, value: string) => {
    setTeamMembers(prev => 
      prev.map(m => 
        m.id === id ? { ...m, [field]: value } : m
      )
    );
  };
  
  const generateDocument = () => {
    toast({
      title: "Document Generated",
      description: "Your document has been successfully generated and is ready for download.",
    });
  };
  
  const saveTemplate = () => {
    toast({
      title: "Template Saved",
      description: "Your template has been saved to the cloud.",
    });
  };
  
  const handleFormLayoutChange = (layout: string) => {
    setFormLayout(layout);
    toast({
      title: "Layout Changed",
      description: `Form layout changed to ${layout} view.`,
    });
  };
  
  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
    toast({
      title: "Theme Changed",
      description: `Switched to ${theme === "light" ? "dark" : "light"} mode.`,
    });
  };

  const toggleSection = (sectionId: string) => {
    setActiveSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId) 
        : [...prev, sectionId]
    );
  };

  const focusSection = (sectionId: string) => {
    setFocusedSection(prev => prev === sectionId ? null : sectionId);
  };
  
  return (
    <section id="template-demo" className="py-20 px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <span className="text-xs font-medium bg-primary/10 text-primary rounded-full px-3 py-1 dark:bg-primary/20">
            Interactive Demo
          </span>
          <h2 className="mt-6 text-3xl md:text-4xl font-medium tracking-tight dark:text-white">
            Experience the QuickDocs Interface
          </h2>
          <p className="mt-4 text-muted-foreground max-w-2xl mx-auto">
            See how QuickDocs transforms templates into interactive forms, with multiple layout options, loop groups, and real-time preview.
          </p>
        </motion.div>
        
        <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-border overflow-hidden">
          {/* Header with document info and actions */}
          <div className="border-b border-border dark:border-gray-700">
            <div className="p-4 flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 dark:bg-primary/20 p-2 rounded">
                  <FileText className="text-primary h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium dark:text-white">Client Agreement Template</h3>
                </div>
                <button
                  onClick={() => setHeaderExpanded(!headerExpanded)}
                  className="ml-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  {headerExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </button>
              </div>
              
              <div className="flex gap-2 items-center">
                <div className="flex space-x-2 mr-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className={`${formLayout === 'vertical' ? 'bg-primary/10 text-primary' : ''}`}
                    onClick={() => handleFormLayoutChange('vertical')}
                    title="Vertical Layout"
                  >
                    <LayoutList className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className={`${formLayout === 'tabbed' ? 'bg-primary/10 text-primary' : ''}`}
                    onClick={() => handleFormLayoutChange('tabbed')}
                    title="Tabbed Layout"
                  >
                    <Layers className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className={`${formLayout === 'canvas' ? 'bg-primary/10 text-primary' : ''}`}
                    onClick={() => handleFormLayoutChange('canvas')}
                    title="Canvas Layout"
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                </div>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={toggleTheme}
                  title={theme === "light" ? "Switch to Dark Mode" : "Switch to Light Mode"}
                >
                  {theme === "light" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="gap-2"
                  onClick={() => setPreviewMode(!previewMode)}
                >
                  {previewMode ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  {previewMode ? "Edit Mode" : "Preview"}
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="gap-2"
                  onClick={saveTemplate}
                >
                  <Save className="h-4 w-4" /> Save
                </Button>
                
                <MeowButton size="sm" variant="primary" className="gap-2" onClick={generateDocument}>
                  <Download className="h-4 w-4" /> Generate
                </MeowButton>
              </div>
            </div>
            
            {/* Expandable metadata section */}
            <AnimatePresence>
              {headerExpanded && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden border-t border-border/50 dark:border-gray-700/50"
                >
                  <div className="p-4 grid grid-cols-2 gap-4 bg-secondary/20 dark:bg-gray-800/50">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Calendar className="h-4 w-4" />
                        <span>Created at:</span>
                        <span className="font-medium dark:text-gray-300">
                          {format(createdDate, "MMMM d, yyyy 'at' h:mm a")}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>Last updated:</span>
                        <span className="font-medium dark:text-gray-300">
                          {format(updatedDate, "MMMM d, yyyy 'at' h:mm a")}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Info className="h-4 w-4" />
                        <span>Document ID:</span>
                        <span className="font-medium font-mono text-xs bg-secondary/40 dark:bg-gray-700/60 px-2 py-0.5 rounded dark:text-gray-300">
                          doc_client_agreement_23x8b9z
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <PanelLeft className="h-4 w-4" />
                        <span>Template Version:</span>
                        <span className="font-medium dark:text-gray-300">2.4</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {previewMode ? (
            <div className="p-6">
              <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-border min-h-[600px]">
                <div className="max-w-3xl mx-auto">
                  <div className="text-center mb-8">
                    <h1 className="text-2xl font-bold uppercase dark:text-white">CLIENT AGREEMENT</h1>
                  </div>
                  
                  <p className="mb-6 dark:text-gray-300">
                    This Agreement (the "Agreement") is made and entered into as of [START_DATE], by and between:
                  </p>
                  
                  <p className="mb-6 dark:text-gray-300">
                    <strong>Company Name:</strong> Your Company LLC, a company organized under the laws of Delaware, with its principal place of business at 123 Business Street, City, State ("the Company"); and
                  </p>
                  
                  <p className="mb-6 dark:text-gray-300">
                    <strong>Client:</strong> <span className="bg-yellow-100 dark:bg-yellow-900 px-1">Acme Corporation</span>, with its principal place of business at <span className="bg-yellow-100 dark:bg-yellow-900 px-1">123 Client Avenue, Clientville</span> ("the Client").
                  </p>
                  
                  <h2 className="text-lg font-bold mb-3 mt-8 dark:text-white">1. PROJECT SCOPE</h2>
                  <p className="mb-6 dark:text-gray-300">
                    The Company agrees to provide the following services: <span className="bg-yellow-100 dark:bg-yellow-900 px-1">Website Redesign</span> as outlined in the attached Project Scope document.
                  </p>
                  
                  <h2 className="text-lg font-bold mb-3 dark:text-white">2. TERM</h2>
                  <p className="mb-6 dark:text-gray-300">
                    This Agreement shall commence on <span className="bg-yellow-100 dark:bg-yellow-900 px-1">2023-07-15</span> and continue until <span className="bg-yellow-100 dark:bg-yellow-900 px-1">2023-12-15</span> unless terminated earlier as provided herein.
                  </p>
                  
                  <h2 className="text-lg font-bold mb-3 dark:text-white">3. COMPENSATION</h2>
                  <p className="mb-3 dark:text-gray-300">
                    Client agrees to pay the Company the total sum of <span className="bg-yellow-100 dark:bg-yellow-900 px-1">$25,000</span> for the Services according to the following schedule:
                  </p>
                  
                  <ul className="list-disc pl-8 mb-6 dark:text-gray-300">
                    {milestones.map((milestone) => (
                      <li key={milestone.id} className="mb-2">
                        <strong>{milestone.title}:</strong> <span className="bg-yellow-100 dark:bg-yellow-900 px-1">${milestone.amount}</span> due on <span className="bg-yellow-100 dark:bg-yellow-900 px-1">{milestone.date}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <h2 className="text-lg font-bold mb-3 dark:text-white">4. PROJECT TEAM</h2>
                  <p className="mb-3 dark:text-gray-300">
                    The Company will assign the following team members to work on the Project:
                  </p>
                  
                  <ul className="list-disc pl-8 mb-6 dark:text-gray-300">
                    {teamMembers.map((member) => (
                      <li key={member.id} className="mb-2">
                        <strong>{member.name}:</strong> <span className="bg-yellow-100 dark:bg-yellow-900 px-1">{member.role}</span> at a rate of <span className="bg-yellow-100 dark:bg-yellow-900 px-1">${member.rate}/hour</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <Tabs 
              defaultValue={formLayout} 
              onValueChange={handleFormLayoutChange}
              className="p-6"
            >
              <div className="flex justify-between items-center mb-6">
                <h4 className="text-sm font-medium dark:text-white">Form Layout Options:</h4>
                <TabsList>
                  <TabsTrigger value="vertical" className="gap-1">
                    <AlignLeft className="h-3.5 w-3.5" /> Vertical
                  </TabsTrigger>
                  <TabsTrigger value="tabbed" className="gap-1">
                    <Layers className="h-3.5 w-3.5" /> Tabbed
                  </TabsTrigger>
                  <TabsTrigger value="side-by-side" className="gap-1">
                    <Columns className="h-3.5 w-3.5" /> Side-by-Side
                  </TabsTrigger>
                  <TabsTrigger value="canvas" className="gap-1">
                    <Grid3x3 className="h-3.5 w-3.5" /> Canvas
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="vertical" className="space-y-6">
                {/* Basic Info Group */}
                <div className="border border-border rounded-lg overflow-hidden dark:border-gray-700">
                  <div 
                    className="bg-secondary/40 p-3 flex justify-between items-center cursor-pointer dark:bg-gray-800"
                    onClick={() => toggleGroup("basic-info")}
                  >
                    <h5 className="font-medium text-sm dark:text-white">Basic Information</h5>
                    {expandedGroups["basic-info"] ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                  </div>
                  
                  {expandedGroups["basic-info"] && (
                    <div className="p-4 space-y-4 dark:bg-gray-900">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium dark:text-gray-200">Client Name</label>
                          <input 
                            type="text" 
                            className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                            placeholder="Enter client name"
                            defaultValue="Acme Corporation"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium dark:text-gray-200">Client Representative</label>
                          <input 
                            type="text" 
                            className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                            placeholder="Enter representative name"
                            defaultValue="John Client"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium dark:text-gray-200">Company Address</label>
                        <textarea 
                          className="w-full border border-border rounded-md p-2 text-sm h-20 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                          placeholder="Enter company address"
                          defaultValue="123 Client Avenue, Clientville"
                        />
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Project Details Group */}
                <div className="border border-border rounded-lg overflow-hidden dark:border-gray-700">
                  <div 
                    className="bg-secondary/40 p-3 flex justify-between items-center cursor-pointer dark:bg-gray-800"
                    onClick={() => toggleGroup("project-details")}
                  >
                    <h5 className="font-medium text-sm dark:text-white">Project Details</h5>
                    {expandedGroups["project-details"] ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                  </div>
                  
                  {expandedGroups["project-details"] && (
                    <div className="p-4 space-y-4 dark:bg-gray-900">
                      <div className="space-y-2">
                        <label className="text-sm font-medium dark:text-gray-200">Project Title</label>
                        <input 
                          type="text" 
                          className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                          placeholder="Enter project title"
                          defaultValue="Website Redesign"
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium dark:text-gray-200">Start Date</label>
                          <input 
                            type="date" 
                            className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                            defaultValue="2023-07-15"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium dark:text-gray-200">End Date</label>
                          <input 
                            type="date" 
                            className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                            defaultValue="2023-12-15"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium dark:text-gray-200">Project Scope</label>
                        <textarea 
                          className="w-full border border-border rounded-md p-2 text-sm h-20 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                          placeholder="Describe the project scope"
                          defaultValue="Complete website redesign including homepage, about page, product pages, and contact form. Includes responsive design and basic SEO optimization."
                        />
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Team Members Loop Group */}
                <div className="border border-border rounded-lg overflow-hidden dark:border-gray-700">
                  <div className="bg-secondary/40 dark:bg-gray-800 p-3 flex justify-between items-center">
                    <h5 className="font-medium text-sm dark:text-white">Team Members</h5>
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => toggleGroup("team-members")}
                        className="h-8 w-8 p-0"
                      >
                        {expandedGroups["team-members"] ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                      {expandedGroups["team-members"] && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={addTeamMember}
                          className="h-8 w-8 p-0"
                        >
                          <PlusIcon className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {expandedGroups["team-members"] && (
                    <div className="p-4 dark:bg-gray-900">
                      <div className="border-b border-border mb-4 dark:border-gray-700">
                        <div className="flex overflow-x-auto no-scrollbar">
                          {teamMembers.map((member, index) => (
                            <button
                              key={member.id}
                              onClick={() => setActiveTeamMemberTab(member.id)}
                              className={`px-4 py-2 text-sm whitespace-nowrap border-b-2 ${
                                activeTeamMemberTab === member.id
                                  ? "border-primary text-primary font-medium"
                                  : "border-transparent hover:border-gray-200 dark:hover:border-gray-700"
                              }`}
                            >
                              {member.name ? member.name : `Team Member ${index + 1}`}
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      {teamMembers.map(member => (
                        <div 
                          key={member.id} 
                          className={`space-y-4 ${activeTeamMemberTab === member.id ? "block" : "hidden"}`}
                        >
                          <div className="flex justify-between items-center">
                            <h6 className="text-sm font-medium dark:text-white">
                              {member.name ? member.name : "New Team Member"}
                            </h6>
                            <div className="flex gap-2">
                              <button 
                                onClick={() => removeTeamMember(member.id)}
                                className="text-destructive/70 hover:text-destructive p-1 rounded transition-colors"
                                title="Remove team member"
                              >
                                <Trash2 size={14} />
                              </button>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <label className="text-sm font-medium dark:text-gray-200">Name</label>
                              <input 
                                type="text" 
                                className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                                placeholder="Enter name"
                                value={member.name}
                                onChange={(e) => updateTeamMember(member.id, "name", e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="text-sm font-medium dark:text-gray-200">Role</label>
                              <input 
                                type="text" 
                                className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                                placeholder="Enter role"
                                value={member.role}
                                onChange={(e) => updateTeamMember(member.id, "role", e.target.value)}
                              />
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <label className="text-sm font-medium dark:text-gray-200">Email</label>
                              <input 
                                type="email" 
                                className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                                placeholder="Enter email"
                                value={member.email}
                                onChange={(e) => updateTeamMember(member.id, "email", e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="text-sm font-medium dark:text-gray-200">Hourly Rate ($)</label>
                              <input 
                                type="number" 
                                className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                                placeholder="Enter rate"
                                value={member.rate}
                                onChange={(e) => updateTeamMember(member.id, "rate", e.target.value)}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Milestones Loop Group */}
                <div className="border border-border rounded-lg overflow-hidden dark:border-gray-700">
                  <div className="bg-secondary/40 dark:bg-gray-800 p-3 flex justify-between items-center">
                    <h5 className="font-medium text-sm dark:text-white">Project Milestones</h5>
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => toggleGroup("milestones")}
                        className="h-8 w-8 p-0"
                      >
                        {expandedGroups["milestones"] ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                      {expandedGroups["milestones"] && (
                        <Button 
                          onClick={addMilestone}
                          className="bg-primary/10 text-primary p-1 rounded hover:bg-primary/20 transition-colors"
                          title="Add milestone"
                          variant="ghost"
                          size="sm"
                        >
                          <PlusIcon size={14} />
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  {expandedGroups["milestones"] && (
                    <div className="p-4 dark:bg-gray-900">
                      <div className="border-b border-border mb-4 dark:border-gray-700">
                        <div className="flex overflow-x-auto no-scrollbar">
                          {milestones.map((milestone, index) => (
                            <button
                              key={milestone.id}
                              onClick={() => setActiveMilestoneTab(milestone.id)}
                              className={`px-4 py-2 text-sm whitespace-nowrap border-b-2 ${
                                activeMilestoneTab === milestone.id
                                  ? "border-primary text-primary font-medium"
                                  : "border-transparent hover:border-gray-200 dark:hover:border-gray-700"
                              }`}
                            >
                              {milestone.title}
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      {milestones.map(milestone => (
                        <div 
                          key={milestone.id} 
                          className={`space-y-4 ${activeMilestoneTab === milestone.id ? "block" : "hidden"}`}
                        >
                          <div className="flex justify-between items-center">
                            <h6 className="text-sm font-medium dark:text-white">
                              {milestone.title}
                            </h6>
                            <div className="flex gap-2">
                              <button 
                                onClick={() => duplicateMilestone(milestone.id)}
                                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded transition-colors"
                                title="Duplicate milestone"
                              >
                                <Copy size={14} />
                              </button>
                              <button 
                                onClick={() => removeMilestone(milestone.id)}
                                className="text-destructive/70 hover:text-destructive p-1 rounded transition-colors"
                                title="Remove milestone"
                              >
                                <Trash2 size={14} />
                              </button>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <label className="text-sm font-medium dark:text-gray-200">Title</label>
                              <input 
                                type="text" 
                                className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                                placeholder="Enter title"
                                value={milestone.title}
                                onChange={(e) => updateMilestone(milestone.id, "title", e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="text-sm font-medium dark:text-gray-200">Date</label>
                              <input 
                                type="date" 
                                className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                                value={milestone.date}
                                onChange={(e) => updateMilestone(milestone.id, "date", e.target.value)}
                              />
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <label className="text-sm font-medium dark:text-gray-200">Description</label>
                            <textarea 
                              className="w-full border border-border rounded-md p-2 text-sm h-16 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                              placeholder="Enter description"
                              value={milestone.description}
                              onChange={(e) => updateMilestone(milestone.id, "description", e.target.value)}
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <label className="text-sm font-medium dark:text-gray-200">Amount ($)</label>
                            <input 
                              type="number" 
                              className="w-full border border-border rounded-md p-2 text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" 
                              placeholder="Enter amount"
                              value={milestone.amount}
                              onChange={(e) => updateMilestone(milestone.id, "amount", e.target.value)}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>
              
              {/* Add other TabsContent components for different layout options */}
            </Tabs>
          )}
        </div>
      </div>
    </section>
  );
};

export default TemplateDemo;
