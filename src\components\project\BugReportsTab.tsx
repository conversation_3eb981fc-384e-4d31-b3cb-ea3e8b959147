
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { getStatusBadge } from "@/utils/statusBadge";
import { formatDate } from "@/utils/formatDate";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface BugReport {
  bug: string;
  email: string;
  steps: string;
  date: number;
  status: string;
}

interface BugReportsTabProps {
  bugReports: BugReport[];
  onUpdateStatus?: (index: number, status: string) => void;
}

const BugReportsTab: React.FC<BugReportsTabProps> = ({ bugReports, onUpdateStatus }) => {
  return (
    <div className="space-y-4">
      {bugReports.length > 0 ? (
        bugReports.map((item, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                  <span className="break-words">{item.bug}</span>
                  {getStatusBadge(item.status)}
                </div>
                <span className="text-sm font-normal text-muted-foreground">
                  {formatDate(item.date)}
                </span>
              </CardTitle>
              <CardDescription className="break-all">{item.email}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-slate-50 dark:bg-gray-800 p-3 rounded-md whitespace-pre-line overflow-x-auto">
                {item.steps}
              </div>
              
              {onUpdateStatus && (
                <div className="flex justify-end mt-4">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="w-full sm:w-auto">Update Status</Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "pending")}>
                        Pending
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "fixed")}>
                        Fixed
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </CardContent>
          </Card>
        ))
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No bug reports received yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BugReportsTab;
