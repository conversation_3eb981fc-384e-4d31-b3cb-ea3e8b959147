import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, List, Columns } from "lucide-react";

interface RoadmapItem {
  id: number;
  title: string;
  description: string;
  status: string;
  targetDate: string;
}

interface PublicRoadmapTabProps {
  roadmapItems: RoadmapItem[];
}

const PublicRoadmapTab: React.FC<PublicRoadmapTabProps> = ({
  roadmapItems,
}) => {
  const [viewMode, setViewMode] = useState<"list" | "kanban">("list");

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-amber-100 text-amber-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatus<PERSON>abel = (status: string) => {
    switch (status) {
      case "planned":
        return "Planned";
      case "in_progress":
        return "In Progress";
      case "completed":
        return "Completed";
      default:
        return "Unknown";
    }
  };

  const getKanbanStatusColor = (status: string) => {
    switch (status) {
      case "planned":
        return "bg-blue-50 border-blue-200";
      case "in_progress":
        return "bg-amber-50 border-amber-200";
      case "completed":
        return "bg-green-50 border-green-200";
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  const getItemsByStatus = (status: string) => {
    return roadmapItems.filter((item) => item.status === status);
  };

  const statuses = ["planned", "in_progress", "completed"];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Project Roadmap</h2>
        <p className="text-muted-foreground mb-6">
          See what's planned, in progress, and completed
        </p>

        <div className="flex justify-center">
          <div className="flex items-center border rounded-full p-1">
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="h-8"
            >
              List
              <List className="h-4 w-4 ml-1" />
            </Button>
            <Button
              variant={viewMode === "kanban" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("kanban")}
              className="h-8"
            >
              Kanban
              <Columns className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </div>

      {roadmapItems.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-muted-foreground">
              No roadmap items available yet.
            </p>
          </CardContent>
        </Card>
      ) : viewMode === "kanban" ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {statuses.map((status) => {
            const items = getItemsByStatus(status);
            return (
              <div
                key={status}
                className={`rounded-lg border-2 border-dashed p-4 ${getKanbanStatusColor(
                  status
                )}`}
              >
                <h3 className="font-semibold text-lg mb-4 text-center">
                  {getStatusLabel(status)} ({items.length})
                </h3>
                <div className="space-y-3">
                  {items.map((item) => (
                    <Card
                      key={item.id}
                      className="transition-all hover:shadow-md"
                    >
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm font-medium">
                          {item.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                          {item.description}
                        </p>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          {new Date(item.targetDate).toLocaleDateString()}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                  {items.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground text-sm">
                      No items in {getStatusLabel(status).toLowerCase()}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="space-y-4">
          {roadmapItems.map((item) => (
            <Card key={item.id} className="transition-all hover:shadow-md">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <CardTitle className="text-lg">{item.title}</CardTitle>
                      <div
                        className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
                          item.status
                        )}`}
                      >
                        {getStatusLabel(item.status)}
                      </div>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      Target: {new Date(item.targetDate).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default PublicRoadmapTab;
