
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

interface FaqItemsCardProps {
  faq: FaqItem[];
  onFaqChange: (id: number, field: "question" | "answer", value: string) => void;
  onAddFaq: () => void;
  onRemoveFaq: (id: number) => void;
}

const FaqItemsCard: React.FC<FaqItemsCardProps> = ({
  faq,
  onFaqChange,
  onAddFaq,
  onRemoveFaq
}) => {
  return (
    <div className="space-y-4">
      <p className="text-sm text-muted-foreground mb-4">
        Create helpful FAQ items to answer common user questions
      </p>
      
      {faq.length === 0 ? (
        <div className="text-center py-8 border-2 border-dashed border-border rounded-lg">
          <p className="text-muted-foreground mb-4">No FAQ items yet</p>
          <Button onClick={onAddFaq} variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Add Your First FAQ
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {faq.map((item, index) => (
            <div key={item.id} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">FAQ #{index + 1}</h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => onRemoveFaq(item.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor={`question-${item.id}`}>Question</Label>
                <Input
                  id={`question-${item.id}`}
                  value={item.question}
                  onChange={(e) => onFaqChange(item.id, "question", e.target.value)}
                  placeholder="What is your return policy?"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`answer-${item.id}`}>Answer</Label>
                <Textarea
                  id={`answer-${item.id}`}
                  value={item.answer}
                  onChange={(e) => onFaqChange(item.id, "answer", e.target.value)}
                  placeholder="We offer a 30-day return policy for all unused items..."
                  rows={3}
                />
              </div>
            </div>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={onAddFaq}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another FAQ Item
          </Button>
        </div>
      )}
    </div>
  );
};

export default FaqItemsCard;
