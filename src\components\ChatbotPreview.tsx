
import React, { useState } from "react";
import { MessageSquare, Bug, PlusCircle, Phone, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type ChatbotPreviewProps = {
  project: {
    greeting: string;
    enabled: {
      feedback: boolean;
      bug: boolean;
      feature: boolean;
      faq: boolean;
      contact: boolean;
    };
    contact?: {
      email: string;
      phone: string;
      address: string;
    };
    faq?: Array<{
      id: number;
      question: string;
      answer: string;
    }>;
  };
};

const ChatbotPreview = ({ project }: ChatbotPreviewProps) => {
  const [isOpen, setIsOpen] = useState(true);
  const [activeOption, setActiveOption] = useState<string | null>(null);

  // Count enabled features to determine grid layout
  const enabledFeatures = Object.values(project.enabled).filter(Boolean).length;
  
  return (
    <div className="h-full flex flex-col">
      {isOpen ? (
        <div className="flex flex-col h-full">
          {/* Chatbot Header */}
          <div className="bg-primary text-primary-foreground p-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">FeedMap</h3>
              <button 
                onClick={() => setIsOpen(false)}
                className="text-primary-foreground/80 hover:text-primary-foreground"
              >
                &times;
              </button>
            </div>
          </div>
          
          {/* Chatbot Content */}
          <div className="flex-1 overflow-auto p-4">
            {activeOption ? (
              <div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="mb-4"
                  onClick={() => setActiveOption(null)}
                >
                  ← Back
                </Button>
                
                {activeOption === "feedback" && (
                  <div className="space-y-4">
                    <h4 className="font-bold text-lg">Share your feedback</h4>
                    <p className="text-muted-foreground text-sm">We value your opinion!</p>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Email (optional)</label>
                        <input 
                          type="email" 
                          className="w-full p-2 border rounded"
                          placeholder="<EMAIL>"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Your feedback</label>
                        <textarea 
                          className="w-full p-2 border rounded h-32"
                          placeholder="Tell us what you think..."
                        />
                      </div>
                      
                      <Button className="w-full">Submit Feedback</Button>
                    </div>
                  </div>
                )}
                
                {activeOption === "bug" && (
                  <div className="space-y-4">
                    <h4 className="font-bold text-lg">Report a Bug</h4>
                    <p className="text-muted-foreground text-sm">Help us improve by reporting issues</p>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Bug title</label>
                        <input 
                          type="text" 
                          className="w-full p-2 border rounded"
                          placeholder="What's the issue?"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Email (optional)</label>
                        <input 
                          type="email" 
                          className="w-full p-2 border rounded"
                          placeholder="<EMAIL>"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Steps to reproduce</label>
                        <textarea 
                          className="w-full p-2 border rounded h-24"
                          placeholder="Describe how to reproduce this bug..."
                        />
                      </div>
                      
                      <Button className="w-full">Submit Bug Report</Button>
                    </div>
                  </div>
                )}
                
                {activeOption === "feature" && (
                  <div className="space-y-4">
                    <h4 className="font-bold text-lg">Request a Feature</h4>
                    <p className="text-muted-foreground text-sm">Suggest new features or improvements</p>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Feature name</label>
                        <input 
                          type="text" 
                          className="w-full p-2 border rounded"
                          placeholder="What would you like to see?"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Details</label>
                        <textarea 
                          className="w-full p-2 border rounded h-24"
                          placeholder="Describe the feature in detail..."
                        />
                      </div>
                      
                      <Button className="w-full">Submit Feature Request</Button>
                    </div>
                  </div>
                )}
                
                {activeOption === "faq" && (
                  <div className="space-y-4">
                    <h4 className="font-bold text-lg">Frequently Asked Questions</h4>
                    
                    <div className="space-y-4">
                      {project.faq && project.faq.map((item) => (
                        <div key={item.id} className="border rounded p-3">
                          <h5 className="font-medium">{item.question}</h5>
                          <p className="text-sm text-muted-foreground mt-1">{item.answer}</p>
                        </div>
                      ))}
                      
                      {(!project.faq || project.faq.length === 0) && (
                        <p className="text-muted-foreground">No FAQs available yet.</p>
                      )}
                    </div>
                  </div>
                )}
                
                {activeOption === "contact" && (
                  <div className="space-y-4">
                    <h4 className="font-bold text-lg">Contact Us</h4>
                    <p className="text-muted-foreground text-sm">Get in touch with our team</p>
                    
                    <div className="space-y-4">
                      {project.contact?.email && (
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 p-2 rounded-full">
                            <MessageSquare className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Email</p>
                            <p className="text-sm text-muted-foreground">{project.contact.email}</p>
                          </div>
                        </div>
                      )}
                      
                      {project.contact?.phone && (
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 p-2 rounded-full">
                            <Phone className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">Phone</p>
                            <p className="text-sm text-muted-foreground">{project.contact.phone}</p>
                          </div>
                        </div>
                      )}
                      
                      {project.contact?.address && (
                        <div className="flex items-center gap-2">
                          <div className="bg-primary/10 p-2 rounded-full flex-shrink-0">
                            <svg className="h-4 w-4 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M12 13V21M12 13C14.7614 13 17 10.7614 17 8C17 5.23858 14.7614 3 12 3C9.23858 3 7 5.23858 7 8C7 10.7614 9.23858 13 12 13ZM17 21H7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Address</p>
                            <p className="text-sm text-muted-foreground">{project.contact.address}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-bold">{project.greeting}</h3>
                  <p className="text-muted-foreground text-sm mt-1">How can we help you today?</p>
                </div>
                
                <div className={cn(
                  "grid gap-2",
                  enabledFeatures <= 2 ? "grid-cols-1" : "grid-cols-2"
                )}>
                  {project.enabled.feedback && (
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 text-left"
                      onClick={() => setActiveOption("feedback")}
                    >
                      <MessageSquare className="h-6 w-6 mb-2" />
                      <div>
                        <p className="font-medium">Feedback</p>
                        <p className="text-xs text-muted-foreground">Share your thoughts</p>
                      </div>
                    </Button>
                  )}
                  
                  {project.enabled.bug && (
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 text-left"
                      onClick={() => setActiveOption("bug")}
                    >
                      <Bug className="h-6 w-6 mb-2" />
                      <div>
                        <p className="font-medium">Report Bug</p>
                        <p className="text-xs text-muted-foreground">Help us improve</p>
                      </div>
                    </Button>
                  )}
                  
                  {project.enabled.feature && (
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 text-left"
                      onClick={() => setActiveOption("feature")}
                    >
                      <PlusCircle className="h-6 w-6 mb-2" />
                      <div>
                        <p className="font-medium">Feature</p>
                        <p className="text-xs text-muted-foreground">Suggest ideas</p>
                      </div>
                    </Button>
                  )}
                  
                  {project.enabled.faq && (
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 text-left"
                      onClick={() => setActiveOption("faq")}
                    >
                      <HelpCircle className="h-6 w-6 mb-2" />
                      <div>
                        <p className="font-medium">FAQ</p>
                        <p className="text-xs text-muted-foreground">Common questions</p>
                      </div>
                    </Button>
                  )}
                  
                  {project.enabled.contact && (
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 text-left"
                      onClick={() => setActiveOption("contact")}
                    >
                      <Phone className="h-6 w-6 mb-2" />
                      <div>
                        <p className="font-medium">Contact</p>
                        <p className="text-xs text-muted-foreground">Get in touch</p>
                      </div>
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
          
          {/* Chatbot Footer */}
          <div className="border-t p-3 text-xs text-center text-muted-foreground">
            Powered by FeedMap
          </div>
        </div>
      ) : (
        <div className="absolute right-4 bottom-4">
          <button
            className="bg-primary text-primary-foreground p-4 rounded-full shadow-lg hover:bg-primary/90 transition-colors"
            onClick={() => setIsOpen(true)}
          >
            <MessageSquare className="h-6 w-6" />
          </button>
        </div>
      )}
    </div>
  );
};

export default ChatbotPreview;
