
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ThumbsUp } from "lucide-react";
import { formatDate } from "@/utils/formatDate";

interface UpvoteItem {
  id: number;
  feature: string;
  detail: string;
  upvotes: number;
  date: number;
}

interface PublicUpvotesTabProps {
  upvotes: UpvoteItem[];
  onVote?: (id: number) => void;
}

const PublicUpvotesTab: React.FC<PublicUpvotesTabProps> = ({ upvotes, onVote }) => {
  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold mb-2">Feature Upvotes</h2>
        <p className="text-muted-foreground">
          Vote for the features you'd like to see implemented
        </p>
      </div>

      {upvotes.length > 0 ? (
        upvotes
          .sort((a, b) => b.upvotes - a.upvotes)
          .map((item) => (
            <Card key={item.id} className="transition-all hover:shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                    <span className="break-words">{item.feature}</span>
                    <span className="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full flex items-center gap-1 w-fit">
                      <ThumbsUp className="h-3 w-3" /> {item.upvotes}
                    </span>
                  </div>
                  <span className="text-sm font-normal text-muted-foreground">
                    {formatDate(item.date)}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-4 break-words">{item.detail}</p>
                
                {onVote && (
                  <div className="flex justify-end mt-4">
                    <Button 
                      size="sm"
                      onClick={() => onVote(item.id)}
                      className="flex items-center gap-2"
                    >
                      <ThumbsUp className="h-4 w-4" /> Vote
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
      ) : (
        <Card>
          <CardContent className="py-12 text-center">
            <ThumbsUp className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-muted-foreground">No features available for voting yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PublicUpvotesTab;
