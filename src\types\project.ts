

export interface ProjectContact {
  address: string;
  phone: string;
  email: string;
}

export interface ProjectEnabledFeatures {
  feedback: boolean;
  bug: boolean;
  feature: boolean;
  faq: boolean;
  contact: boolean;
}

export interface FeedbackItem {
  email: string;
  feedback: string;
  date: number;
  status: string;
}

export interface BugReport {
  bug: string;
  email: string;
  steps: string;
  date: number;
  status: string;
}

export interface FeatureRequest {
  feature: string;
  detail: string;
  date: number;
  status: string;
}

export interface FaqItem {
  question: string;
  answer: string;
  id: number;
}

export interface RoadmapItem {
  id: number;
  title: string;
  description: string;
  status: string;
  targetDate: string;
}

export interface UpvoteItem {
  id: number;
  feature: string;
  detail: string;
  upvotes: number;
  date: number;
}

export interface Project {
  id: string;
  name: string;
  domain: string;
  greeting: string;
  feedback: FeedbackItem[];
  bug: BugReport[];
  feature: FeatureRequest[];
  contact: ProjectContact;
  enabled: ProjectEnabledFeatures;
  faq: FaqItem[];
  roadmap: RoadmapItem[];
  upvotes: UpvoteItem[];
}

