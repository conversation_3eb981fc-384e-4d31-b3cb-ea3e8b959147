
import React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const BillingSettings = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing Information</CardTitle>
        <CardDescription>
          Manage your subscription and payment details
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-secondary/30 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">Current Plan</h3>
              <p className="text-xl font-bold">Professional</p>
              <p className="text-sm text-muted-foreground mt-1">$29/month • Renews on June 1, 2025</p>
            </div>
            <Button variant="outline">Change Plan</Button>
          </div>
        </div>
        
        <div className="mt-6">
          <h3 className="font-medium mb-4">Payment Method</h3>
          <div className="flex items-center justify-between p-4 border border-border rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-10 h-6 bg-blue-600 rounded flex items-center justify-center text-white font-bold text-sm">
                VISA
              </div>
              <div>
                <p className="font-medium">Visa ending in 4242</p>
                <p className="text-sm text-muted-foreground">Expires 04/2025</p>
              </div>
            </div>
            <Button variant="ghost">Update</Button>
          </div>
        </div>
        
        <div className="mt-6">
          <h3 className="font-medium mb-2">Billing History</h3>
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Amount</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
                <th className="px-4 py-2 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">Receipt</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              <tr>
                <td className="px-4 py-2 text-sm">May 1, 2025</td>
                <td className="px-4 py-2 text-sm">$29.00</td>
                <td className="px-4 py-2 text-sm"><span className="text-green-600">Paid</span></td>
                <td className="px-4 py-2 text-sm text-right"><Button variant="link" className="p-0 h-auto">Download</Button></td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm">Apr 1, 2025</td>
                <td className="px-4 py-2 text-sm">$29.00</td>
                <td className="px-4 py-2 text-sm text-right"><span className="text-green-600">Paid</span></td>
                <td className="px-4 py-2 text-sm text-right"><Button variant="link" className="p-0 h-auto">Download</Button></td>
              </tr>
              <tr>
                <td className="px-4 py-2 text-sm">Mar 1, 2025</td>
                <td className="px-4 py-2 text-sm">$29.00</td>
                <td className="px-4 py-2 text-sm"><span className="text-green-600">Paid</span></td>
                <td className="px-4 py-2 text-sm text-right"><Button variant="link" className="p-0 h-auto">Download</Button></td>
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

export default BillingSettings;
