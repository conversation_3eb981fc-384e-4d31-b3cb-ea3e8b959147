import React from "react";
import { Button } from "@/components/ui/button";
import { Settings, Share2, ExternalLink } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ProjectHeaderProps {
  name: string;
  domain: string;
  onSettingsClick: () => void;
}

const ProjectHeader: React.FC<ProjectHeaderProps> = ({
  name,
  domain,
  onSettingsClick,
}) => {
  const { toast } = useToast();

  const handleSharePublicLink = () => {
    const publicUrl = `${window.location.origin}/public/${
      window.location.pathname.split("/")[2]
    }`;
    navigator.clipboard.writeText(publicUrl);

    toast({
      title: "Link copied!",
      description: "Public project link has been copied to clipboard.",
    });
  };

  const handleOpenPublicView = () => {
    const publicUrl = `${window.location.origin}/public/${
      window.location.pathname.split("/")[2]
    }`;
    window.open(publicUrl, "_blank");
  };

  return (
    <div className="mb-8 pb-6 border-b border-gray-200">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{name}</h1>
          <p className="text-gray-600">{domain}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSharePublicLink}
            className="flex items-center gap-2"
          >
            <Share2 className="h-4 w-4" />
            Share Public Link
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenPublicView}
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            View Public
          </Button>
          <Button
            size="sm"
            onClick={onSettingsClick}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProjectHeader;
