
import React, { useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";

const designPrinciples = [
  {
    number: "01",
    title: "Simplicity",
    description:
      "Eliminating the unnecessary to focus on what truly matters. Every element has a purpose.",
  },
  {
    number: "02",
    title: "Functionality",
    description:
      "Design that enhances usability, making complex tasks feel effortless and intuitive.",
  },
  {
    number: "03",
    title: "Harmony",
    description:
      "Creating a cohesive experience where all elements work together seamlessly.",
  },
  {
    number: "04",
    title: "Longevity",
    description:
      "Timeless design that transcends trends, remaining relevant and functional for years.",
  },
];

const Design = () => {
  const targetRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: targetRef,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);

  return (
    <section id="design" className="py-24 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-6 md:px-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <span className="text-xs font-medium bg-secondary rounded-full px-3 py-1">
            Design Philosophy
          </span>
          <h2 className="mt-6 text-3xl md:text-4xl font-medium tracking-tight">
            Our Design Principles
          </h2>
          <p className="mt-4 text-muted-foreground max-w-2xl mx-auto">
            Guided by a commitment to excellence, our design philosophy embraces simplicity, functionality, and beauty.
          </p>
        </motion.div>

        <div 
          ref={targetRef}
          className="relative z-10 grid grid-cols-1 md:grid-cols-2 gap-12 md:gap-16"
        >
          {designPrinciples.map((principle, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white rounded-xl p-8 shadow-sm border border-gray-50 hover:shadow-md transition-all duration-300"
            >
              <span className="block text-4xl font-light text-primary/25 mb-4">
                {principle.number}
              </span>
              <h3 className="text-xl font-medium mb-3">{principle.title}</h3>
              <p className="text-muted-foreground">{principle.description}</p>
            </motion.div>
          ))}
        </div>

        <motion.div 
          style={{ y }}
          className="absolute top-1/2 -right-64 w-96 h-96 rounded-full bg-secondary/30 blur-3xl -z-10" 
        />
        <motion.div 
          style={{ y: useTransform(scrollYProgress, [0, 1], [-100, 100]) }}
          className="absolute bottom-0 -left-32 w-64 h-64 rounded-full bg-secondary/30 blur-3xl -z-10" 
        />
      </div>
    </section>
  );
};

export default Design;
