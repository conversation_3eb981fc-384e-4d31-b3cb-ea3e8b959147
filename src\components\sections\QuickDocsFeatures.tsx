
import React from "react";
import { motion } from "framer-motion";
import { MessageSquare, BarChart3, <PERSON><PERSON>s, Zap, Users, Shield } from "lucide-react";

const features = [
  {
    icon: MessageSquare,
    title: "Customizable Feedback Widgets",
    description: "Create beautiful, on-brand feedback widgets that integrate seamlessly with your website or application."
  },
  {
    icon: BarChart3,
    title: "Advanced Analytics",
    description: "Get detailed insights into user feedback patterns with comprehensive analytics and reporting tools."
  },
  {
    icon: Settings,
    title: "Easy Integration",
    description: "Simple setup process with flexible configuration options to match your specific needs and branding."
  },
  {
    icon: Zap,
    title: "Real-time Updates",
    description: "Receive instant notifications when new feedback arrives and respond quickly to user concerns."
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Work together with your team to manage feedback, assign tasks, and track resolution progress."
  },
  {
    icon: Shield,
    title: "Secure & Reliable",
    description: "Enterprise-grade security ensures your feedback data is protected and always available when you need it."
  }
];

const QuickDocsFeatures = () => {
  return (
    <section id="features" className="py-20 px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="text-primary font-medium text-sm uppercase tracking-wide">
            Features
          </span>
          <h2 className="mt-4 text-3xl md:text-4xl font-medium text-foreground">
            Everything you need to manage feedback
          </h2>
          <p className="mt-4 text-xl text-muted-foreground max-w-2xl mx-auto">
            FeedMap provides all the tools you need to collect, analyze, and act on user feedback effectively.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-card p-6 rounded-lg border border-border hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <feature.icon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="ml-3 text-lg font-medium text-foreground">
                  {feature.title}
                </h3>
              </div>
              <p className="text-muted-foreground">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default QuickDocsFeatures;
