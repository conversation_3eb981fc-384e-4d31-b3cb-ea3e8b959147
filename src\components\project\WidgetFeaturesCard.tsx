
import React from "react";
import { Switch } from "@/components/ui/switch";

interface WidgetFeaturesCardProps {
  enabled: {
    feedback: boolean;
    bug: boolean;
    feature: boolean;
    faq: boolean;
    contact: boolean;
  };
  onToggleChange: (feature: keyof WidgetFeaturesCardProps['enabled']) => void;
}

const features = [
  {
    key: "feedback" as const,
    title: "Feedback",
    description: "Allow users to submit general feedback"
  },
  {
    key: "bug" as const,
    title: "Bug Reports", 
    description: "Allow users to report bugs"
  },
  {
    key: "feature" as const,
    title: "Feature Requests",
    description: "Allow users to request new features"
  },
  {
    key: "faq" as const,
    title: "FAQ",
    description: "Di<PERSON>lay frequently asked questions"
  },
  {
    key: "contact" as const,
    title: "Contact Information",
    description: "Show contact details in the widget"
  }
];

const WidgetFeaturesCard: React.FC<WidgetFeaturesCardProps> = ({ enabled, onToggleChange }) => {
  return (
    <div className="space-y-6">
      {features.map((feature) => {
        const isEnabled = enabled[feature.key];
        
        return (
          <div
            key={feature.key}
            className="flex items-center justify-between"
          >
            <div>
              <h3 className="font-medium text-foreground">{feature.title}</h3>
              <p className="text-sm text-muted-foreground">
                {feature.description}
              </p>
            </div>
            <Switch
              checked={isEnabled}
              onCheckedChange={() => onToggleChange(feature.key)}
            />
          </div>
        );
      })}
    </div>
  );
};

export default WidgetFeaturesCard;
