
export const useFaqHandlers = (
  formData: any,
  setFormData: React.Dispatch<React.SetStateAction<any>>
) => {
  const handleFaqChange = (id: number, field: "question" | "answer", value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      faq: prev.faq.map((item: any) => 
        item.id === id ? { ...item, [field]: value } : item
      )
    }));
  };

  const handleAddFaq = () => {
    const newId = formData.faq.length > 0 
      ? Math.max(...formData.faq.map((item: any) => item.id)) + 1 
      : 0;
    
    setFormData((prev: any) => ({
      ...prev,
      faq: [...prev.faq, { id: newId, question: "", answer: "" }]
    }));
  };

  const handleRemoveFaq = (id: number) => {
    setFormData((prev: any) => ({
      ...prev,
      faq: prev.faq.filter((item: any) => item.id !== id)
    }));
  };

  return {
    handleFaqChange,
    handleAddFaq,
    handleRemoveFaq
  };
};
