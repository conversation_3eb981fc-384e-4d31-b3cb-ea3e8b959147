
import { Project } from "@/types/project";

export const useRoadmapChangelogHandlers = (
  project: Project,
  setProject: React.Dispatch<React.SetStateAction<Project>>,
  changelog: any[],
  setChangelog: React.Dispatch<React.SetStateAction<any[]>>
) => {
  const handleAddRoadmapItem = (newItem: any) => {
    const newRoadmapItem = {
      id: project.roadmap.length + 1,
      ...newItem
    };

    setProject(prev => ({
      ...prev,
      roadmap: [...prev.roadmap, newRoadmapItem]
    }));
  };

  const handleRemoveRoadmapItem = (id: number) => {
    setProject(prev => ({
      ...prev,
      roadmap: prev.roadmap.filter(item => item.id !== id)
    }));
  };

  const handleAddChangelogEntry = (newEntry: any) => {
    const newChangelogEntry = {
      id: changelog.length + 1,
      date: Date.now(),
      ...newEntry
    };

    setChangelog(prev => [newChangelogEntry, ...prev]);
  };

  const handleRemoveChangelogEntry = (id: number) => {
    setChangelog(prev => prev.filter(entry => entry.id !== id));
  };

  return {
    handleAddRoadmapItem,
    handleRemoveRoadmapItem,
    handleAddChangelogEntry,
    handleRemoveChangelogEntry
  };
};
