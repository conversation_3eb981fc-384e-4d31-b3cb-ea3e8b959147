
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Calendar, ThumbsUp, BarChart2, ScrollText } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { projectData, changelogData } from "@/data/mockProjectData";
import PublicUpvotesTab from "@/components/public/PublicUpvotesTab";
import PublicRoadmapTab from "@/components/public/PublicRoadmapTab";
import PublicChangelogTab from "@/components/public/PublicChangelogTab";

const PublicProject = () => {
  const { projectId } = useParams();
  const [activeTab, setActiveTab] = useState("upvotes");

  // In a real app, you would fetch project data based on projectId
  const project = projectData;
  const changelog = changelogData;

  const handleVote = (id: number) => {
    // In a real app, this would make an API call
    console.log(`Voting for upvote item ${id}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto py-6 px-4">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{project.name}</h1>
            <p className="text-gray-600">Public Project View</p>
            <div className="mt-4">
              <Badge variant="outline" className="text-sm">
                {project.domain}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto py-8 px-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="flex justify-center mb-8">
            <TabsList className="grid w-full max-w-md grid-cols-3">
              <TabsTrigger value="upvotes" className="flex items-center gap-2">
                <ThumbsUp className="h-4 w-4" />
                <span className="hidden sm:inline">Upvotes</span>
              </TabsTrigger>
              <TabsTrigger value="roadmap" className="flex items-center gap-2">
                <BarChart2 className="h-4 w-4" />
                <span className="hidden sm:inline">Roadmap</span>
              </TabsTrigger>
              <TabsTrigger value="changelog" className="flex items-center gap-2">
                <ScrollText className="h-4 w-4" />
                <span className="hidden sm:inline">Changelog</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="upvotes">
            <PublicUpvotesTab upvotes={project.upvotes} onVote={handleVote} />
          </TabsContent>

          <TabsContent value="roadmap">
            <PublicRoadmapTab roadmapItems={project.roadmap} />
          </TabsContent>

          <TabsContent value="changelog">
            <PublicChangelogTab changelog={changelog} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 mt-16">
        <div className="container mx-auto py-6 px-4 text-center">
          <p className="text-gray-500 text-sm">
            Powered by {project.name} • Public Project View
          </p>
        </div>
      </div>
    </div>
  );
};

export default PublicProject;
