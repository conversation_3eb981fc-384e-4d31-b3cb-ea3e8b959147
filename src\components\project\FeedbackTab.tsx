
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { getStatusBadge } from "@/utils/statusBadge";
import { formatDate } from "@/utils/formatDate";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface FeedbackItem {
  email: string;
  feedback: string;
  date: number;
  status: string;
}

interface FeedbackTabProps {
  feedback: FeedbackItem[];
  onUpdateStatus?: (index: number, status: string) => void;
}

const FeedbackTab: React.FC<FeedbackTabProps> = ({ feedback, onUpdateStatus }) => {
  return (
    <div className="space-y-4">
      {feedback.length > 0 ? (
        feedback.map((item, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3">
                <div className="flex flex-col sm:flex-row sm:items-center gap-3 min-w-0 flex-1">
                  <span className="break-all text-base sm:text-lg">{item.email}</span>
                  {getStatusBadge(item.status)}
                </div>
                <span className="text-sm font-normal text-muted-foreground whitespace-nowrap">
                  {formatDate(item.date)}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4 break-words text-sm sm:text-base leading-relaxed">{item.feedback}</p>
              
              {onUpdateStatus && (
                <div className="flex justify-end mt-4">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="w-full sm:w-auto">
                        Update Status
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-white dark:bg-gray-800 border shadow-lg z-50">
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "unread")}>
                        Unread
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "read")}>
                        Read
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "completed")}>
                        Completed
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </CardContent>
          </Card>
        ))
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No feedback received yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FeedbackTab;
